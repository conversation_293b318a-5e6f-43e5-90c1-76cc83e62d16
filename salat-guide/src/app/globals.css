@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Bengali:wght@300;400;500;600;700&family=Amiri:wght@400;700&display=swap');
@import "tailwindcss";

:root {
  /* Light theme colors */
  --background: #f8fafc;
  --foreground: #374151;
  --card: #ffffff;
  --card-foreground: #374151;
  --popover: #ffffff;
  --popover-foreground: #374151;
  --primary: #10b981;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #475569;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #f59e0b;
  --accent-foreground: #ffffff;
  --destructive: #dc2626;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #10b981;
  --radius: 0.5rem;

  /* Islamic design colors */
  --emerald: #10b981;
  --gold: #f59e0b;
  --sapphire: #3b82f6;
  --pearl: #f8fafc;
  --ruby: #dc2626;
  --onyx: #374151;

  /* Geometric pattern opacity */
  --pattern-opacity: 0.05;
}

[data-theme="dark"] {
  /* Dark theme colors */
  --background: #0f172a;
  --foreground: #f1f5f9;
  --card: #1e293b;
  --card-foreground: #f1f5f9;
  --popover: #1e293b;
  --popover-foreground: #f1f5f9;
  --primary: #34d399;
  --primary-foreground: #0f172a;
  --secondary: #334155;
  --secondary-foreground: #cbd5e1;
  --muted: #334155;
  --muted-foreground: #94a3b8;
  --accent: #fbbf24;
  --accent-foreground: #0f172a;
  --destructive: #f87171;
  --destructive-foreground: #0f172a;
  --border: #334155;
  --input: #334155;
  --ring: #34d399;

  /* Islamic design colors - dark variants */
  --emerald: #34d399;
  --gold: #fbbf24;
  --sapphire: #60a5fa;
  --pearl: #1e293b;
  --ruby: #f87171;
  --onyx: #9ca3af;

  /* Geometric pattern opacity */
  --pattern-opacity: 0.1;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Noto Sans Bengali', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Arabic text styling */
.arabic-text {
  font-family: 'Amiri', serif;
  direction: rtl;
  text-align: right;
  line-height: 1.8;
}

/* Bengali text styling */
.bengali-text {
  font-family: 'Noto Sans Bengali', sans-serif;
  line-height: 1.7;
}

/* Geometric pattern background */
.geometric-pattern {
  position: relative;
  overflow: hidden;
}

.geometric-pattern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: var(--pattern-opacity);
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='1'%3E%3Cpath d='M30 30l15-15v30l-15-15zm-15 0l15 15v-30l-15 15z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  pointer-events: none;
  z-index: -1;
}

/* Smooth transitions */
.transition-theme {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary));
}
