import { NextIntlClientProvider } from 'next-intl'
import { getMessages, getTranslations } from 'next-intl/server'
import { notFound } from 'next/navigation'
import { ThemeProvider } from '@/lib/providers/theme-provider'
import { MainLayout } from '@/components/layout/main-layout'
import { locales } from '@/lib/i18n/config'
import { generateMetadata as generateMetadataUtil } from '@/lib/utils/metadata'

interface LocaleLayoutProps {
  children: React.ReactNode
  params: Promise<{ locale: string }>
}

export function generateStaticParams() {
  return locales.map((locale) => ({ locale }))
}

export async function generateMetadata({ params }: LocaleLayoutProps) {
  const { locale } = await params
  const t = await getTranslations({ locale, namespace: 'meta' })

  return generateMetadataUtil({
    title: t('title'),
    description: t('description'),
    keywords: t('keywords').split(', '),
    locale,
  })
}

export default async function LocaleLayout({
  children,
  params
}: LocaleLayoutProps) {
  const { locale } = await params

  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) {
    notFound()
  }

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages()

  return (
    <NextIntlClientProvider messages={messages}>
      <ThemeProvider
        attribute="data-theme"
        defaultTheme="light"
        enableSystem
        disableTransitionOnChange={false}
      >
        <MainLayout>
          {children}
        </MainLayout>
      </ThemeProvider>
    </NextIntlClientProvider>
  )
}
