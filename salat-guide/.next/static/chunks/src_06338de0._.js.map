{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/providers/theme-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { ThemeProvider as NextThemesProvider } from 'next-themes'\nimport type { ThemeProviderProps } from 'next-themes/dist/types'\n\ntype Theme = 'dark' | 'light' | 'system'\n\ntype ThemeProviderContext = {\n  theme: string\n  setTheme: (theme: Theme) => void\n}\n\nconst ThemeProviderContext = createContext<ThemeProviderContext | undefined>(\n  undefined\n)\n\nexport function ThemeProvider({\n  children,\n  ...props\n}: ThemeProviderProps) {\n  return (\n    <NextThemesProvider\n      attribute=\"data-theme\"\n      defaultTheme=\"light\"\n      enableSystem\n      disableTransitionOnChange={false}\n      {...props}\n    >\n      {children}\n    </NextThemesProvider>\n  )\n}\n\nexport const useTheme = () => {\n  const context = useContext(ThemeProviderContext)\n\n  if (context === undefined)\n    throw new Error('useTheme must be used within a ThemeProvider')\n\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAaA,MAAM,qCAAuB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACvC;AAGK,SAAS,cAAc,KAGT;QAHS,EAC5B,QAAQ,EACR,GAAG,OACgB,GAHS;IAI5B,qBACE,6LAAC,mJAAA,CAAA,gBAAkB;QACjB,WAAU;QACV,cAAa;QACb,YAAY;QACZ,2BAA2B;QAC1B,GAAG,KAAK;kBAER;;;;;;AAGP;KAfgB;AAiBT,MAAM,WAAW;;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WACd,MAAM,IAAI,MAAM;IAElB,OAAO;AACT;GAPa", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/utils/cn.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\n/**\n * Utility function to merge Tailwind CSS classes with clsx\n * Handles conditional classes and removes conflicts\n */\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAMO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/utils/index.ts"], "sourcesContent": ["export { cn } from './cn'\n\n/**\n * Format date for display\n */\nexport function formatDate(date: Date | string, locale: string = 'bn'): string {\n  const dateObj = typeof date === 'string' ? new Date(date) : date\n  \n  return new Intl.DateTimeFormat(locale, {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(dateObj)\n}\n\n/**\n * Generate slug from title\n */\nexport function generateSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\n/**\n * Debounce function\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null\n  \n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\n/**\n * Check if device supports touch\n */\nexport function isTouchDevice(): boolean {\n  return typeof window !== 'undefined' && 'ontouchstart' in window\n}\n\n/**\n * Get viewport dimensions\n */\nexport function getViewportDimensions() {\n  if (typeof window === 'undefined') {\n    return { width: 0, height: 0 }\n  }\n  \n  return {\n    width: window.innerWidth,\n    height: window.innerHeight,\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAKO,SAAS,WAAW,IAAmB;QAAE,SAAA,iEAAiB;IAC/D,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,OAAO,IAAI,KAAK,cAAc,CAAC,QAAQ;QACrC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAKO,SAAS,aAAa,KAAa;IACxC,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAKO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO;yCAAI;YAAA;;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAKO,SAAS;IACd,OAAO,aAAkB,eAAe,kBAAkB;AAC5D;AAKO,SAAS;IACd;;IAIA,OAAO;QACL,OAAO,OAAO,UAAU;QACxB,QAAQ,OAAO,WAAW;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/constants/design.ts"], "sourcesContent": ["import type { IslamicColor, GeometricPattern, LayoutConfig } from '@/types/global'\n\n/**\n * Islamic color palette for light and dark themes\n */\nexport const ISLAMIC_COLORS: Record<string, IslamicColor> = {\n  emerald: {\n    name: 'Emerald',\n    light: '#10b981',\n    dark: '#34d399',\n    description: 'Traditional Islamic green representing paradise and nature'\n  },\n  gold: {\n    name: 'Gold',\n    light: '#f59e0b',\n    dark: '#fbbf24',\n    description: 'Sacred gold representing divine light and wisdom'\n  },\n  sapphire: {\n    name: 'Sapphire',\n    light: '#3b82f6',\n    dark: '#60a5fa',\n    description: 'Deep blue representing the heavens and spirituality'\n  },\n  pearl: {\n    name: '<PERSON>',\n    light: '#f8fafc',\n    dark: '#1e293b',\n    description: 'Pure white/dark representing purity and clarity'\n  },\n  ruby: {\n    name: 'Ruby',\n    light: '#dc2626',\n    dark: '#f87171',\n    description: 'Rich red for accents and important elements'\n  },\n  onyx: {\n    name: 'Onyx',\n    light: '#374151',\n    dark: '#9ca3af',\n    description: 'Neutral dark/light for text and secondary elements'\n  }\n}\n\n/**\n * Islamic geometric patterns (SVG paths)\n */\nexport const GEOMETRIC_PATTERNS: GeometricPattern[] = [\n  {\n    id: 'octagon-star',\n    name: 'Octagon Star',\n    viewBox: '0 0 100 100',\n    svg: `<path d=\"M50 10 L70 30 L90 30 L90 50 L70 70 L50 90 L30 70 L10 50 L10 30 L30 30 Z\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>\n          <path d=\"M50 25 L65 40 L65 60 L50 75 L35 60 L35 40 Z\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>`\n  },\n  {\n    id: 'arabesque',\n    name: 'Arabesque',\n    viewBox: '0 0 100 100',\n    svg: `<path d=\"M20 50 Q30 30, 50 50 Q70 70, 80 50 Q70 30, 50 50 Q30 70, 20 50\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>\n          <path d=\"M50 20 Q70 30, 50 50 Q30 70, 50 80 Q70 70, 50 50 Q30 30, 50 20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>`\n  },\n  {\n    id: 'geometric-grid',\n    name: 'Geometric Grid',\n    viewBox: '0 0 100 100',\n    svg: `<defs>\n            <pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n              <path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"0.5\"/>\n            </pattern>\n          </defs>\n          <rect width=\"100\" height=\"100\" fill=\"url(#grid)\"/>`\n  }\n]\n\n/**\n * Layout configuration\n */\nexport const LAYOUT_CONFIG: LayoutConfig = {\n  sidebar: {\n    width: 280,\n    collapsedWidth: 64,\n    breakpoint: 768, // md breakpoint\n  },\n  header: {\n    height: 64,\n  },\n  content: {\n    maxWidth: 1200,\n    padding: 24,\n  }\n}\n\n/**\n * Animation durations and easings\n */\nexport const ANIMATIONS = {\n  durations: {\n    fast: 150,\n    normal: 300,\n    slow: 500,\n  },\n  easings: {\n    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',\n    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n  }\n}\n\n/**\n * Breakpoints for responsive design\n */\nexport const BREAKPOINTS = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536,\n} as const\n"], "names": [], "mappings": ";;;;;;;AAKO,MAAM,iBAA+C;IAC1D,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,MAAM;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,UAAU;QACR,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,OAAO;QACL,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,MAAM;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,MAAM;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;AACF;AAKO,MAAM,qBAAyC;IACpD;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,KAAM;IAER;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,KAAM;IAER;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,KAAM;IAMR;CACD;AAKM,MAAM,gBAA8B;IACzC,SAAS;QACP,OAAO;QACP,gBAAgB;QAChB,YAAY;IACd;IACA,QAAQ;QACN,QAAQ;IACV;IACA,SAAS;QACP,UAAU;QACV,SAAS;IACX;AACF;AAKO,MAAM,aAAa;IACxB,WAAW;QACT,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,WAAW;QACX,SAAS;QACT,QAAQ;IACV;AACF;AAKO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/constants/navigation.ts"], "sourcesContent": ["import type { NavigationItem } from '@/types/global'\n\n/**\n * Main navigation structure for the Salat Guide\n */\nexport const NAVIGATION_ITEMS: NavigationItem[] = [\n  {\n    id: 'home',\n    title: 'হোম',\n    href: '/',\n    icon: 'Home',\n  },\n  {\n    id: 'prayer-times',\n    title: 'নামাজের সময়',\n    href: '/prayer-times',\n    icon: 'Clock',\n  },\n  {\n    id: 'prayer-guide',\n    title: 'নামাজের নিয়ম',\n    icon: 'Book',\n    children: [\n      {\n        id: 'wudu',\n        title: 'অজু',\n        href: '/prayer-guide/wudu',\n        icon: 'Droplets',\n      },\n      {\n        id: 'fajr',\n        title: 'ফজর',\n        href: '/prayer-guide/fajr',\n        icon: 'Sunrise',\n      },\n      {\n        id: 'dhuhr',\n        title: 'যুহর',\n        href: '/prayer-guide/dhuhr',\n        icon: 'Sun',\n      },\n      {\n        id: 'asr',\n        title: 'আসর',\n        href: '/prayer-guide/asr',\n        icon: 'CloudSun',\n      },\n      {\n        id: 'maghrib',\n        title: 'মাগরিব',\n        href: '/prayer-guide/maghrib',\n        icon: 'Sunset',\n      },\n      {\n        id: 'isha',\n        title: 'ইশা',\n        href: '/prayer-guide/isha',\n        icon: 'Moon',\n      },\n    ],\n  },\n  {\n    id: 'duas',\n    title: 'দোয়া সমূহ',\n    icon: 'Heart',\n    children: [\n      {\n        id: 'daily-duas',\n        title: 'দৈনন্দিন দোয়া',\n        href: '/duas/daily',\n        icon: 'Calendar',\n      },\n      {\n        id: 'prayer-duas',\n        title: 'নামাজের দোয়া',\n        href: '/duas/prayer',\n        icon: 'Hands',\n      },\n      {\n        id: 'special-duas',\n        title: 'বিশেষ দোয়া',\n        href: '/duas/special',\n        icon: 'Star',\n      },\n    ],\n  },\n  {\n    id: 'qibla',\n    title: 'কিবলার দিক',\n    href: '/qibla',\n    icon: 'Compass',\n  },\n  {\n    id: 'islamic-calendar',\n    title: 'ইসলামিক ক্যালেন্ডার',\n    href: '/calendar',\n    icon: 'CalendarDays',\n  },\n  {\n    id: 'learning',\n    title: 'শিক্ষা',\n    icon: 'GraduationCap',\n    children: [\n      {\n        id: 'basics',\n        title: 'মৌলিক বিষয়',\n        href: '/learning/basics',\n        icon: 'BookOpen',\n      },\n      {\n        id: 'arabic',\n        title: 'আরবি শেখা',\n        href: '/learning/arabic',\n        icon: 'Languages',\n      },\n      {\n        id: 'quran',\n        title: 'কুরআন তিলাওয়াত',\n        href: '/learning/quran',\n        icon: 'BookMarked',\n      },\n    ],\n  },\n  {\n    id: 'settings',\n    title: 'সেটিংস',\n    href: '/settings',\n    icon: 'Settings',\n  },\n]\n\n/**\n * Footer navigation links\n */\nexport const FOOTER_LINKS = [\n  {\n    title: 'সম্পর্কে',\n    href: '/about',\n  },\n  {\n    title: 'যোগাযোগ',\n    href: '/contact',\n  },\n  {\n    title: 'গোপনীয়তা নীতি',\n    href: '/privacy',\n  },\n  {\n    title: 'ব্যবহারের শর্তাবলী',\n    href: '/terms',\n  },\n]\n\n/**\n * Social media links\n */\nexport const SOCIAL_LINKS = [\n  {\n    name: 'Facebook',\n    href: '#',\n    icon: 'Facebook',\n  },\n  {\n    name: 'Twitter',\n    href: '#',\n    icon: 'Twitter',\n  },\n  {\n    name: 'Instagram',\n    href: '#',\n    icon: 'Instagram',\n  },\n  {\n    name: 'YouTube',\n    href: '#',\n    icon: 'Youtube',\n  },\n]\n"], "names": [], "mappings": ";;;;;AAKO,MAAM,mBAAqC;IAChD;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;CACD;AAKM,MAAM,eAAe;IAC1B;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;CACD;AAKM,MAAM,eAAe;IAC1B;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/constants/index.ts"], "sourcesContent": ["export * from './design'\nexport * from './navigation'\n\n/**\n * Application metadata\n */\nexport const APP_CONFIG = {\n  name: 'Salat Guide',\n  description: 'Complete Islamic prayer guide in Bengali',\n  version: '1.0.0',\n  author: 'Salat Guide Team',\n  keywords: ['islam', 'prayer', 'salat', 'bengali', 'muslim', 'guide'],\n  url: 'https://salat-guide.com',\n  locale: {\n    default: 'bn',\n    supported: ['bn', 'en', 'ar'],\n  },\n} as const\n\n/**\n * API endpoints and external services\n */\nexport const API_CONFIG = {\n  prayerTimes: 'https://api.aladhan.com/v1',\n  qibla: 'https://api.aladhan.com/v1/qibla',\n  islamicCalendar: 'https://api.aladhan.com/v1/gToH',\n} as const\n\n/**\n * Local storage keys\n */\nexport const STORAGE_KEYS = {\n  theme: 'salat-guide-theme',\n  locale: 'salat-guide-locale',\n  location: 'salat-guide-location',\n  sidebarState: 'salat-guide-sidebar',\n  userPreferences: 'salat-guide-preferences',\n} as const\n\n/**\n * Default prayer times (fallback)\n */\nexport const DEFAULT_PRAYER_TIMES = {\n  fajr: '05:30',\n  dhuhr: '12:15',\n  asr: '15:45',\n  maghrib: '18:30',\n  isha: '20:00',\n} as const\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;IACR,UAAU;QAAC;QAAS;QAAU;QAAS;QAAW;QAAU;KAAQ;IACpE,KAAK;IACL,QAAQ;QACN,SAAS;QACT,WAAW;YAAC;YAAM;YAAM;SAAK;IAC/B;AACF;AAKO,MAAM,aAAa;IACxB,aAAa;IACb,OAAO;IACP,iBAAiB;AACnB;AAKO,MAAM,eAAe;IAC1B,OAAO;IACP,QAAQ;IACR,UAAU;IACV,cAAc;IACd,iBAAiB;AACnB;AAKO,MAAM,uBAAuB;IAClC,MAAM;IACN,OAAO;IACP,KAAK;IACL,SAAS;IACT,MAAM;AACR", "debugId": null}}, {"offset": {"line": 489, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/hooks/use-sidebar.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { STORAGE_KEYS, BREAKPOINTS } from '@/lib/constants'\nimport { getViewportDimensions, debounce } from '@/lib/utils'\nimport type { SidebarState } from '@/types/global'\n\nexport function useSidebar() {\n  const [sidebarState, setSidebarState] = useState<SidebarState>({\n    isOpen: false,\n    isMobile: false,\n  })\n\n  // Check if device is mobile\n  const checkIsMobile = useCallback(() => {\n    const { width } = getViewportDimensions()\n    return width < BREAKPOINTS.md\n  }, [])\n\n  // Load sidebar state from localStorage\n  const loadSidebarState = useCallback(() => {\n    if (typeof window === 'undefined') return\n\n    try {\n      const saved = localStorage.getItem(STORAGE_KEYS.sidebarState)\n      const isMobile = checkIsMobile()\n      \n      if (saved) {\n        const parsedState = JSON.parse(saved) as SidebarState\n        setSidebarState({\n          isOpen: isMobile ? false : parsedState.isOpen, // Always closed on mobile initially\n          isMobile,\n        })\n      } else {\n        setSidebarState({\n          isOpen: !isMobile, // Open by default on desktop, closed on mobile\n          isMobile,\n        })\n      }\n    } catch (error) {\n      console.error('Failed to load sidebar state:', error)\n      setSidebarState({\n        isOpen: !checkIsMobile(),\n        isMobile: checkIsMobile(),\n      })\n    }\n  }, [checkIsMobile])\n\n  // Save sidebar state to localStorage\n  const saveSidebarState = useCallback((state: SidebarState) => {\n    if (typeof window === 'undefined') return\n\n    try {\n      localStorage.setItem(STORAGE_KEYS.sidebarState, JSON.stringify(state))\n    } catch (error) {\n      console.error('Failed to save sidebar state:', error)\n    }\n  }, [])\n\n  // Toggle sidebar\n  const toggleSidebar = useCallback(() => {\n    setSidebarState(prev => {\n      const newState = { ...prev, isOpen: !prev.isOpen }\n      saveSidebarState(newState)\n      return newState\n    })\n  }, [saveSidebarState])\n\n  // Open sidebar\n  const openSidebar = useCallback(() => {\n    setSidebarState(prev => {\n      const newState = { ...prev, isOpen: true }\n      saveSidebarState(newState)\n      return newState\n    })\n  }, [saveSidebarState])\n\n  // Close sidebar\n  const closeSidebar = useCallback(() => {\n    setSidebarState(prev => {\n      const newState = { ...prev, isOpen: false }\n      saveSidebarState(newState)\n      return newState\n    })\n  }, [saveSidebarState])\n\n  // Handle window resize\n  const handleResize = useCallback(\n    debounce(() => {\n      const isMobile = checkIsMobile()\n      setSidebarState(prev => {\n        const newState = {\n          ...prev,\n          isMobile,\n          isOpen: isMobile ? false : prev.isOpen, // Close on mobile when resizing\n        }\n        saveSidebarState(newState)\n        return newState\n      })\n    }, 150),\n    [checkIsMobile, saveSidebarState]\n  )\n\n  // Initialize and handle resize\n  useEffect(() => {\n    loadSidebarState()\n    \n    window.addEventListener('resize', handleResize)\n    return () => window.removeEventListener('resize', handleResize)\n  }, [loadSidebarState, handleResize])\n\n  return {\n    ...sidebarState,\n    toggleSidebar,\n    openSidebar,\n    closeSidebar,\n  }\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;;AAJA;;;;AAOO,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,QAAQ;QACR,UAAU;IACZ;IAEA,4BAA4B;IAC5B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAChC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+IAAA,CAAA,wBAAqB,AAAD;YACtC,OAAO,QAAQ,oIAAA,CAAA,cAAW,CAAC,EAAE;QAC/B;gDAAG,EAAE;IAEL,uCAAuC;IACvC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YACnC;;YAEA,IAAI;gBACF,MAAM,QAAQ,aAAa,OAAO,CAAC,mJAAA,CAAA,eAAY,CAAC,YAAY;gBAC5D,MAAM,WAAW;gBAEjB,IAAI,OAAO;oBACT,MAAM,cAAc,KAAK,KAAK,CAAC;oBAC/B,gBAAgB;wBACd,QAAQ,WAAW,QAAQ,YAAY,MAAM;wBAC7C;oBACF;gBACF,OAAO;oBACL,gBAAgB;wBACd,QAAQ,CAAC;wBACT;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,gBAAgB;oBACd,QAAQ,CAAC;oBACT,UAAU;gBACZ;YACF;QACF;mDAAG;QAAC;KAAc;IAElB,qCAAqC;IACrC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACpC;;YAEA,IAAI;gBACF,aAAa,OAAO,CAAC,mJAAA,CAAA,eAAY,CAAC,YAAY,EAAE,KAAK,SAAS,CAAC;YACjE,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;mDAAG,EAAE;IAEL,iBAAiB;IACjB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE;YAChC;yDAAgB,CAAA;oBACd,MAAM,WAAW;wBAAE,GAAG,IAAI;wBAAE,QAAQ,CAAC,KAAK,MAAM;oBAAC;oBACjD,iBAAiB;oBACjB,OAAO;gBACT;;QACF;gDAAG;QAAC;KAAiB;IAErB,eAAe;IACf,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE;YAC9B;uDAAgB,CAAA;oBACd,MAAM,WAAW;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAK;oBACzC,iBAAiB;oBACjB,OAAO;gBACT;;QACF;8CAAG;QAAC;KAAiB;IAErB,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC/B;wDAAgB,CAAA;oBACd,MAAM,WAAW;wBAAE,GAAG,IAAI;wBAAE,QAAQ;oBAAM;oBAC1C,iBAAiB;oBACjB,OAAO;gBACT;;QACF;+CAAG;QAAC;KAAiB;IAErB,uBAAuB;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD,EAC7B,CAAA,GAAA,+IAAA,CAAA,WAAQ,AAAD;gDAAE;YACP,MAAM,WAAW;YACjB;wDAAgB,CAAA;oBACd,MAAM,WAAW;wBACf,GAAG,IAAI;wBACP;wBACA,QAAQ,WAAW,QAAQ,KAAK,MAAM;oBACxC;oBACA,iBAAiB;oBACjB,OAAO;gBACT;;QACF;+CAAG,MACH;QAAC;QAAe;KAAiB;IAGnC,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;wCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;+BAAG;QAAC;QAAkB;KAAa;IAEnC,OAAO;QACL,GAAG,YAAY;QACf;QACA;QACA;IACF;AACF;GA9GgB", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/hooks/use-swipe.ts"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef, useCallback } from 'react'\nimport { isTouchDevice } from '@/lib/utils'\nimport type { SwipeGestureConfig } from '@/types/global'\n\ninterface SwipeHandlers {\n  onSwipeLeft?: () => void\n  onSwipeRight?: () => void\n  onSwipeUp?: () => void\n  onSwipeDown?: () => void\n}\n\ninterface TouchPoint {\n  x: number\n  y: number\n  time: number\n}\n\nconst DEFAULT_CONFIG: SwipeGestureConfig = {\n  threshold: 50, // Minimum distance for swipe\n  velocity: 0.3, // Minimum velocity (pixels per ms)\n  direction: 'right',\n}\n\nexport function useSwipe(\n  handlers: SwipeHandlers,\n  config: Partial<SwipeGestureConfig> = {}\n) {\n  const touchStart = useRef<TouchPoint | null>(null)\n  const touchEnd = useRef<TouchPoint | null>(null)\n  const elementRef = useRef<HTMLElement | null>(null)\n\n  const finalConfig = { ...DEFAULT_CONFIG, ...config }\n\n  const handleTouchStart = useCallback((e: TouchEvent) => {\n    if (!isTouchDevice()) return\n\n    const touch = e.touches[0]\n    if (!touch) return\n\n    touchStart.current = {\n      x: touch.clientX,\n      y: touch.clientY,\n      time: Date.now(),\n    }\n    touchEnd.current = null\n  }, [])\n\n  const handleTouchMove = useCallback((e: TouchEvent) => {\n    if (!touchStart.current) return\n\n    const touch = e.touches[0]\n    if (!touch) return\n\n    touchEnd.current = {\n      x: touch.clientX,\n      y: touch.clientY,\n      time: Date.now(),\n    }\n  }, [])\n\n  const handleTouchEnd = useCallback(() => {\n    if (!touchStart.current || !touchEnd.current) return\n\n    const deltaX = touchEnd.current.x - touchStart.current.x\n    const deltaY = touchEnd.current.y - touchStart.current.y\n    const deltaTime = touchEnd.current.time - touchStart.current.time\n\n    const distanceX = Math.abs(deltaX)\n    const distanceY = Math.abs(deltaY)\n    const velocity = Math.max(distanceX, distanceY) / deltaTime\n\n    // Check if swipe meets minimum requirements\n    if (velocity < finalConfig.velocity) return\n\n    // Determine swipe direction\n    if (distanceX > distanceY) {\n      // Horizontal swipe\n      if (distanceX > finalConfig.threshold) {\n        if (deltaX > 0) {\n          handlers.onSwipeRight?.()\n        } else {\n          handlers.onSwipeLeft?.()\n        }\n      }\n    } else {\n      // Vertical swipe\n      if (distanceY > finalConfig.threshold) {\n        if (deltaY > 0) {\n          handlers.onSwipeDown?.()\n        } else {\n          handlers.onSwipeUp?.()\n        }\n      }\n    }\n\n    // Reset\n    touchStart.current = null\n    touchEnd.current = null\n  }, [handlers, finalConfig])\n\n  useEffect(() => {\n    const element = elementRef.current\n    if (!element || !isTouchDevice()) return\n\n    // Add passive listeners for better performance\n    element.addEventListener('touchstart', handleTouchStart, { passive: true })\n    element.addEventListener('touchmove', handleTouchMove, { passive: true })\n    element.addEventListener('touchend', handleTouchEnd, { passive: true })\n\n    return () => {\n      element.removeEventListener('touchstart', handleTouchStart)\n      element.removeEventListener('touchmove', handleTouchMove)\n      element.removeEventListener('touchend', handleTouchEnd)\n    }\n  }, [handleTouchStart, handleTouchMove, handleTouchEnd])\n\n  return elementRef\n}\n"], "names": [], "mappings": ";;;AAEA;AACA;AAAA;;AAHA;;;AAmBA,MAAM,iBAAqC;IACzC,WAAW;IACX,UAAU;IACV,WAAW;AACb;AAEO,SAAS,SACd,QAAuB;QACvB,SAAA,iEAAsC,CAAC;;IAEvC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC7C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAsB;IAE9C,MAAM,cAAc;QAAE,GAAG,cAAc;QAAE,GAAG,MAAM;IAAC;IAEnD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACpC,IAAI,CAAC,CAAA,GAAA,+IAAA,CAAA,gBAAa,AAAD,KAAK;YAEtB,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO;YAEZ,WAAW,OAAO,GAAG;gBACnB,GAAG,MAAM,OAAO;gBAChB,GAAG,MAAM,OAAO;gBAChB,MAAM,KAAK,GAAG;YAChB;YACA,SAAS,OAAO,GAAG;QACrB;iDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,CAAC;YACnC,IAAI,CAAC,WAAW,OAAO,EAAE;YAEzB,MAAM,QAAQ,EAAE,OAAO,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO;YAEZ,SAAS,OAAO,GAAG;gBACjB,GAAG,MAAM,OAAO;gBAChB,GAAG,MAAM,OAAO;gBAChB,MAAM,KAAK,GAAG;YAChB;QACF;gDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YACjC,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE;YAE9C,MAAM,SAAS,SAAS,OAAO,CAAC,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC;YACxD,MAAM,SAAS,SAAS,OAAO,CAAC,CAAC,GAAG,WAAW,OAAO,CAAC,CAAC;YACxD,MAAM,YAAY,SAAS,OAAO,CAAC,IAAI,GAAG,WAAW,OAAO,CAAC,IAAI;YAEjE,MAAM,YAAY,KAAK,GAAG,CAAC;YAC3B,MAAM,YAAY,KAAK,GAAG,CAAC;YAC3B,MAAM,WAAW,KAAK,GAAG,CAAC,WAAW,aAAa;YAElD,4CAA4C;YAC5C,IAAI,WAAW,YAAY,QAAQ,EAAE;YAErC,4BAA4B;YAC5B,IAAI,YAAY,WAAW;gBACzB,mBAAmB;gBACnB,IAAI,YAAY,YAAY,SAAS,EAAE;oBACrC,IAAI,SAAS,GAAG;4BACd;yBAAA,yBAAA,SAAS,YAAY,cAArB,6CAAA,4BAAA;oBACF,OAAO;4BACL;yBAAA,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA;oBACF;gBACF;YACF,OAAO;gBACL,iBAAiB;gBACjB,IAAI,YAAY,YAAY,SAAS,EAAE;oBACrC,IAAI,SAAS,GAAG;4BACd;yBAAA,wBAAA,SAAS,WAAW,cAApB,4CAAA,2BAAA;oBACF,OAAO;4BACL;yBAAA,sBAAA,SAAS,SAAS,cAAlB,0CAAA,yBAAA;oBACF;gBACF;YACF;YAEA,QAAQ;YACR,WAAW,OAAO,GAAG;YACrB,SAAS,OAAO,GAAG;QACrB;+CAAG;QAAC;QAAU;KAAY;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,+IAAA,CAAA,gBAAa,AAAD,KAAK;YAElC,+CAA+C;YAC/C,QAAQ,gBAAgB,CAAC,cAAc,kBAAkB;gBAAE,SAAS;YAAK;YACzE,QAAQ,gBAAgB,CAAC,aAAa,iBAAiB;gBAAE,SAAS;YAAK;YACvE,QAAQ,gBAAgB,CAAC,YAAY,gBAAgB;gBAAE,SAAS;YAAK;YAErE;sCAAO;oBACL,QAAQ,mBAAmB,CAAC,cAAc;oBAC1C,QAAQ,mBAAmB,CAAC,aAAa;oBACzC,QAAQ,mBAAmB,CAAC,YAAY;gBAC1C;;QACF;6BAAG;QAAC;QAAkB;QAAiB;KAAe;IAEtD,OAAO;AACT;GA9FgB", "debugId": null}}, {"offset": {"line": 792, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/hooks/index.ts"], "sourcesContent": ["export { useSidebar } from './use-sidebar'\nexport { useSwipe } from './use-swipe'\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 814, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/theme/geometric-pattern.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\nimport { GEOMETRIC_PATTERNS } from '@/lib/constants'\nimport type { GeometricPattern } from '@/types/global'\n\ninterface GeometricPatternProps {\n  pattern?: GeometricPattern\n  className?: string\n  opacity?: number\n  color?: string\n}\n\nexport function GeometricPatternComponent({\n  pattern = GEOMETRIC_PATTERNS[0],\n  className,\n  opacity = 0.05,\n  color = 'currentColor',\n}: GeometricPatternProps) {\n  const patternId = `pattern-${pattern.id}`\n\n  return (\n    <div\n      className={cn(\n        'absolute inset-0 pointer-events-none overflow-hidden',\n        className\n      )}\n      style={{ opacity }}\n    >\n      <svg\n        className=\"absolute inset-0 h-full w-full\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        <defs>\n          <pattern\n            id={patternId}\n            x=\"0\"\n            y=\"0\"\n            width=\"100\"\n            height=\"100\"\n            patternUnits=\"userSpaceOnUse\"\n          >\n            <g\n              dangerouslySetInnerHTML={{ __html: pattern.svg }}\n              style={{ color }}\n            />\n          </pattern>\n        </defs>\n        <rect\n          width=\"100%\"\n          height=\"100%\"\n          fill={`url(#${patternId})`}\n        />\n      </svg>\n    </div>\n  )\n}\n\n// Pre-built pattern components for common use cases\nexport function OctagonStarPattern(props: Omit<GeometricPatternProps, 'pattern'>) {\n  return (\n    <GeometricPatternComponent\n      pattern={GEOMETRIC_PATTERNS.find(p => p.id === 'octagon-star')}\n      {...props}\n    />\n  )\n}\n\nexport function ArabesquePattern(props: Omit<GeometricPatternProps, 'pattern'>) {\n  return (\n    <GeometricPatternComponent\n      pattern={GEOMETRIC_PATTERNS.find(p => p.id === 'arabesque')}\n      {...props}\n    />\n  )\n}\n\nexport function GeometricGridPattern(props: Omit<GeometricPatternProps, 'pattern'>) {\n  return (\n    <GeometricPatternComponent\n      pattern={GEOMETRIC_PATTERNS.find(p => p.id === 'geometric-grid')}\n      {...props}\n    />\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AACA;AAAA;AAHA;;;;AAaO,SAAS,0BAA0B,KAKlB;QALkB,EACxC,UAAU,oIAAA,CAAA,qBAAkB,CAAC,EAAE,EAC/B,SAAS,EACT,UAAU,IAAI,EACd,QAAQ,cAAc,EACA,GALkB;IAMxC,MAAM,YAAY,AAAC,WAAqB,OAAX,QAAQ,EAAE;IAEvC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAEF,OAAO;YAAE;QAAQ;kBAEjB,cAAA,6LAAC;YACC,WAAU;YACV,OAAM;;8BAEN,6LAAC;8BACC,cAAA,6LAAC;wBACC,IAAI;wBACJ,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,cAAa;kCAEb,cAAA,6LAAC;4BACC,yBAAyB;gCAAE,QAAQ,QAAQ,GAAG;4BAAC;4BAC/C,OAAO;gCAAE;4BAAM;;;;;;;;;;;;;;;;8BAIrB,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,MAAM,AAAC,QAAiB,OAAV,WAAU;;;;;;;;;;;;;;;;;AAKlC;KA3CgB;AA8CT,SAAS,mBAAmB,KAA6C;IAC9E,qBACE,6LAAC;QACC,SAAS,oIAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,GAAG,KAAK;;;;;;AAGf;MAPgB;AAST,SAAS,iBAAiB,KAA6C;IAC5E,qBACE,6LAAC;QACC,SAAS,oIAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,GAAG,KAAK;;;;;;AAGf;MAPgB;AAST,SAAS,qBAAqB,KAA6C;IAChF,qBACE,6LAAC;QACC,SAAS,oIAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,GAAG,KAAK;;;;;;AAGf;MAPgB", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/navigation/sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { ChevronDown, ChevronRight } from 'lucide-react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { cn } from '@/lib/utils'\nimport { NAVIGATION_ITEMS, LAYOUT_CONFIG } from '@/lib/constants'\nimport { useSidebar, useSwipe } from '@/lib/hooks'\nimport { GeometricGridPattern } from '@/components/theme/geometric-pattern'\nimport type { NavigationItem } from '@/types/global'\n\ninterface SidebarProps {\n  className?: string\n}\n\ninterface NavigationItemProps {\n  item: NavigationItem\n  level?: number\n  isCollapsed?: boolean\n}\n\nfunction NavigationItemComponent({ \n  item, \n  level = 0, \n  isCollapsed = false \n}: NavigationItemProps) {\n  const [isExpanded, setIsExpanded] = useState(item.isExpanded ?? false)\n  const pathname = usePathname()\n  const isActive = pathname === item.href\n  const hasChildren = item.children && item.children.length > 0\n\n  const toggleExpanded = () => {\n    if (hasChildren) {\n      setIsExpanded(!isExpanded)\n    }\n  }\n\n  const itemContent = (\n    <div\n      className={cn(\n        'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200',\n        'hover:bg-accent hover:text-accent-foreground',\n        isActive && 'bg-primary text-primary-foreground',\n        level > 0 && 'ml-4',\n        isCollapsed && 'justify-center px-2'\n      )}\n    >\n      {item.icon && (\n        <span className=\"flex-shrink-0\">\n          {/* Icon would be rendered here - using placeholder for now */}\n          <div className=\"h-5 w-5 rounded bg-current opacity-60\" />\n        </span>\n      )}\n      {!isCollapsed && (\n        <>\n          <span className=\"flex-1 truncate\">{item.title}</span>\n          {hasChildren && (\n            <span className=\"flex-shrink-0\">\n              {isExpanded ? (\n                <ChevronDown className=\"h-4 w-4\" />\n              ) : (\n                <ChevronRight className=\"h-4 w-4\" />\n              )}\n            </span>\n          )}\n        </>\n      )}\n    </div>\n  )\n\n  return (\n    <div>\n      {item.href ? (\n        <Link href={item.href} onClick={toggleExpanded}>\n          {itemContent}\n        </Link>\n      ) : (\n        <button\n          onClick={toggleExpanded}\n          className=\"w-full text-left\"\n          disabled={isCollapsed && hasChildren}\n        >\n          {itemContent}\n        </button>\n      )}\n\n      {/* Children */}\n      <AnimatePresence>\n        {hasChildren && isExpanded && !isCollapsed && (\n          <motion.div\n            initial={{ height: 0, opacity: 0 }}\n            animate={{ height: 'auto', opacity: 1 }}\n            exit={{ height: 0, opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"overflow-hidden\"\n          >\n            <div className=\"mt-1 space-y-1\">\n              {item.children?.map((child) => (\n                <NavigationItemComponent\n                  key={child.id}\n                  item={child}\n                  level={level + 1}\n                  isCollapsed={isCollapsed}\n                />\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n\nexport function Sidebar({ className }: SidebarProps) {\n  const { isOpen, isMobile, closeSidebar } = useSidebar()\n  \n  // Swipe gesture for mobile\n  const swipeRef = useSwipe({\n    onSwipeLeft: () => {\n      if (isMobile && isOpen) {\n        closeSidebar()\n      }\n    },\n  })\n\n  const sidebarWidth = isOpen \n    ? LAYOUT_CONFIG.sidebar.width \n    : LAYOUT_CONFIG.sidebar.collapsedWidth\n\n  return (\n    <>\n      {/* Mobile overlay */}\n      <AnimatePresence>\n        {isMobile && isOpen && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.2 }}\n            className=\"fixed inset-0 z-40 bg-black/50 lg:hidden\"\n            onClick={closeSidebar}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.aside\n        ref={swipeRef}\n        initial={false}\n        animate={{\n          width: isMobile ? (isOpen ? sidebarWidth : 0) : sidebarWidth,\n          x: isMobile && !isOpen ? -sidebarWidth : 0,\n        }}\n        transition={{ duration: 0.3, ease: 'easeInOut' }}\n        className={cn(\n          'fixed left-0 top-0 z-50 h-full bg-card border-r border-border overflow-hidden',\n          'lg:relative lg:z-auto',\n          className\n        )}\n      >\n        {/* Background pattern */}\n        <GeometricGridPattern opacity={0.03} />\n        \n        <div className=\"relative flex h-full flex-col\">\n          {/* Header */}\n          <div className=\"flex h-16 items-center border-b border-border px-4\">\n            {isOpen && (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.1 }}\n                className=\"flex items-center gap-3\"\n              >\n                <div className=\"h-8 w-8 rounded-lg bg-primary\" />\n                <span className=\"font-bold text-lg\">Salat Guide</span>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 overflow-y-auto p-4\">\n            <div className=\"space-y-2\">\n              {NAVIGATION_ITEMS.map((item) => (\n                <NavigationItemComponent\n                  key={item.id}\n                  item={item}\n                  isCollapsed={!isOpen}\n                />\n              ))}\n            </div>\n          </nav>\n\n          {/* Footer */}\n          {isOpen && (\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.2 }}\n              className=\"border-t border-border p-4\"\n            >\n              <div className=\"text-xs text-muted-foreground\">\n                <p>Salat Guide v1.0.0</p>\n                <p>Islamic Prayer Guide</p>\n              </div>\n            </motion.div>\n          )}\n        </div>\n      </motion.aside>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;;;AAVA;;;;;;;;;;AAuBA,SAAS,wBAAwB,KAIX;QAJW,EAC/B,IAAI,EACJ,QAAQ,CAAC,EACT,cAAc,KAAK,EACC,GAJW;QA4ElB;;QAvEgC;IAA7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,mBAAA,KAAK,UAAU,cAAf,8BAAA,mBAAmB;IAChE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,WAAW,aAAa,KAAK,IAAI;IACvC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;IAE5D,MAAM,iBAAiB;QACrB,IAAI,aAAa;YACf,cAAc,CAAC;QACjB;IACF;IAEA,MAAM,4BACJ,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gGACA,gDACA,YAAY,sCACZ,QAAQ,KAAK,QACb,eAAe;;YAGhB,KAAK,IAAI,kBACR,6LAAC;gBAAK,WAAU;0BAEd,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;YAGlB,CAAC,6BACA;;kCACE,6LAAC;wBAAK,WAAU;kCAAmB,KAAK,KAAK;;;;;;oBAC5C,6BACC,6LAAC;wBAAK,WAAU;kCACb,2BACC,6LAAC,uNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;iDAEvB,6LAAC,yNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;IAStC,qBACE,6LAAC;;YACE,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;gBAAC,MAAM,KAAK,IAAI;gBAAE,SAAS;0BAC7B;;;;;qCAGH,6LAAC;gBACC,SAAS;gBACT,WAAU;gBACV,UAAU,eAAe;0BAExB;;;;;;0BAKL,6LAAC,4LAAA,CAAA,kBAAe;0BACb,eAAe,cAAc,CAAC,6BAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBACjC,SAAS;wBAAE,QAAQ;wBAAQ,SAAS;oBAAE;oBACtC,MAAM;wBAAE,QAAQ;wBAAG,SAAS;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;mCACZ,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,GAAG,CAAC,CAAC,sBACnB,6LAAC;gCAEC,MAAM;gCACN,OAAO,QAAQ;gCACf,aAAa;+BAHR,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/B;GA1FS;;QAMU,qIAAA,CAAA,cAAW;;;KANrB;AA4FF,SAAS,QAAQ,KAA2B;QAA3B,EAAE,SAAS,EAAgB,GAA3B;;IACtB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAEpD,2BAA2B;IAC3B,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE;QACxB,WAAW;0CAAE;gBACX,IAAI,YAAY,QAAQ;oBACtB;gBACF;YACF;;IACF;IAEA,MAAM,eAAe,SACjB,oIAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,KAAK,GAC3B,oIAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,cAAc;IAExC,qBACE;;0BAEE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,YAAY,wBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;oBACV,SAAS;;;;;;;;;;;0BAMf,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,KAAK;gBACL,SAAS;gBACT,SAAS;oBACP,OAAO,WAAY,SAAS,eAAe,IAAK;oBAChD,GAAG,YAAY,CAAC,SAAS,CAAC,eAAe;gBAC3C;gBACA,YAAY;oBAAE,UAAU;oBAAK,MAAM;gBAAY;gBAC/C,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,iFACA,yBACA;;kCAIF,6LAAC,sJAAA,CAAA,uBAAoB;wBAAC,SAAS;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;0CACZ,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACZ,wIAAA,CAAA,mBAAgB,CAAC,GAAG,CAAC,CAAC,qBACrB,6LAAC;4CAEC,MAAM;4CACN,aAAa,CAAC;2CAFT,KAAK,EAAE;;;;;;;;;;;;;;;4BASnB,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;IAjGgB;;QAC6B,wIAAA,CAAA,aAAU;QAGpC,sIAAA,CAAA,WAAQ;;;MAJX", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/navigation/hamburger-menu.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\nimport { useSidebar } from '@/lib/hooks'\n\ninterface HamburgerMenuProps {\n  className?: string\n  size?: 'sm' | 'md' | 'lg'\n}\n\nexport function HamburgerMenu({ className, size = 'md' }: HamburgerMenuProps) {\n  const { isOpen, toggleSidebar } = useSidebar()\n\n  const lineVariants = {\n    closed: {\n      rotate: 0,\n      y: 0,\n    },\n    open: {\n      rotate: 0,\n      y: 0,\n    },\n  }\n\n  const topLineVariants = {\n    closed: {\n      rotate: 0,\n      y: 0,\n    },\n    open: {\n      rotate: 45,\n      y: size === 'sm' ? 6 : size === 'md' ? 8 : 10,\n    },\n  }\n\n  const bottomLineVariants = {\n    closed: {\n      rotate: 0,\n      y: 0,\n    },\n    open: {\n      rotate: -45,\n      y: size === 'sm' ? -6 : size === 'md' ? -8 : -10,\n    },\n  }\n\n  const middleLineVariants = {\n    closed: {\n      opacity: 1,\n      x: 0,\n    },\n    open: {\n      opacity: 0,\n      x: -20,\n    },\n  }\n\n  const buttonSize = {\n    sm: 'h-8 w-8',\n    md: 'h-10 w-10',\n    lg: 'h-12 w-12',\n  }[size]\n\n  const lineHeight = {\n    sm: 'h-0.5',\n    md: 'h-0.5',\n    lg: 'h-1',\n  }[size]\n\n  const lineWidth = {\n    sm: 'w-4',\n    md: 'w-5',\n    lg: 'w-6',\n  }[size]\n\n  const lineSpacing = {\n    sm: 'space-y-1',\n    md: 'space-y-1.5',\n    lg: 'space-y-2',\n  }[size]\n\n  return (\n    <button\n      onClick={toggleSidebar}\n      className={cn(\n        'relative inline-flex items-center justify-center rounded-lg border border-border bg-card text-card-foreground transition-all duration-200 hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n        buttonSize,\n        className\n      )}\n      aria-label={isOpen ? 'Close menu' : 'Open menu'}\n      aria-expanded={isOpen}\n    >\n      <div className={cn('flex flex-col', lineSpacing)}>\n        <motion.span\n          variants={topLineVariants}\n          animate={isOpen ? 'open' : 'closed'}\n          transition={{ duration: 0.3, ease: 'easeInOut' }}\n          className={cn(\n            'block bg-current transition-all duration-300',\n            lineHeight,\n            lineWidth\n          )}\n        />\n        <motion.span\n          variants={middleLineVariants}\n          animate={isOpen ? 'open' : 'closed'}\n          transition={{ duration: 0.3, ease: 'easeInOut' }}\n          className={cn(\n            'block bg-current transition-all duration-300',\n            lineHeight,\n            lineWidth\n          )}\n        />\n        <motion.span\n          variants={bottomLineVariants}\n          animate={isOpen ? 'open' : 'closed'}\n          transition={{ duration: 0.3, ease: 'easeInOut' }}\n          className={cn(\n            'block bg-current transition-all duration-300',\n            lineHeight,\n            lineWidth\n          )}\n        />\n      </div>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;;;AAJA;;;;AAWO,SAAS,cAAc,KAA8C;QAA9C,EAAE,SAAS,EAAE,OAAO,IAAI,EAAsB,GAA9C;;IAC5B,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAE3C,MAAM,eAAe;QACnB,QAAQ;YACN,QAAQ;YACR,GAAG;QACL;QACA,MAAM;YACJ,QAAQ;YACR,GAAG;QACL;IACF;IAEA,MAAM,kBAAkB;QACtB,QAAQ;YACN,QAAQ;YACR,GAAG;QACL;QACA,MAAM;YACJ,QAAQ;YACR,GAAG,SAAS,OAAO,IAAI,SAAS,OAAO,IAAI;QAC7C;IACF;IAEA,MAAM,qBAAqB;QACzB,QAAQ;YACN,QAAQ;YACR,GAAG;QACL;QACA,MAAM;YACJ,QAAQ,CAAC;YACT,GAAG,SAAS,OAAO,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,CAAC;QAChD;IACF;IAEA,MAAM,qBAAqB;QACzB,QAAQ;YACN,SAAS;YACT,GAAG;QACL;QACA,MAAM;YACJ,SAAS;YACT,GAAG,CAAC;QACN;IACF;IAEA,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,MAAM,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,MAAM,YAAY;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8PACA,YACA;QAEF,cAAY,SAAS,eAAe;QACpC,iBAAe;kBAEf,cAAA,6LAAC;YAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;;8BAClC,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,UAAU;oBACV,SAAS,SAAS,SAAS;oBAC3B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gDACA,YACA;;;;;;8BAGJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,UAAU;oBACV,SAAS,SAAS,SAAS;oBAC3B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gDACA,YACA;;;;;;8BAGJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oBACV,UAAU;oBACV,SAAS,SAAS,SAAS;oBAC3B,YAAY;wBAAE,UAAU;wBAAK,MAAM;oBAAY;oBAC/C,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,gDACA,YACA;;;;;;;;;;;;;;;;;AAMZ;GApHgB;;QACoB,wIAAA,CAAA,aAAU;;;KAD9B", "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/theme/theme-toggle.tsx"], "sourcesContent": ["'use client'\n\nimport { Moon, Sun } from 'lucide-react'\nimport { useTheme } from 'next-themes'\nimport { useEffect, useState } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ThemeToggleProps {\n  className?: string\n  size?: 'sm' | 'md' | 'lg'\n}\n\nexport function ThemeToggle({ className, size = 'md' }: ThemeToggleProps) {\n  const [mounted, setMounted] = useState(false)\n  const { theme, setTheme } = useTheme()\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <div\n        className={cn(\n          'rounded-lg border border-border bg-card',\n          {\n            'h-8 w-8': size === 'sm',\n            'h-10 w-10': size === 'md',\n            'h-12 w-12': size === 'lg',\n          },\n          className\n        )}\n      />\n    )\n  }\n\n  const toggleTheme = () => {\n    setTheme(theme === 'light' ? 'dark' : 'light')\n  }\n\n  const iconSize = {\n    sm: 16,\n    md: 20,\n    lg: 24,\n  }[size]\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className={cn(\n        'relative inline-flex items-center justify-center rounded-lg border border-border bg-card text-card-foreground transition-all duration-300 hover:bg-accent hover:text-accent-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n        {\n          'h-8 w-8': size === 'sm',\n          'h-10 w-10': size === 'md',\n          'h-12 w-12': size === 'lg',\n        },\n        className\n      )}\n      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}\n    >\n      <div className=\"relative\">\n        <Sun\n          size={iconSize}\n          className={cn(\n            'absolute transition-all duration-300',\n            theme === 'dark'\n              ? 'rotate-90 scale-0 opacity-0'\n              : 'rotate-0 scale-100 opacity-100'\n          )}\n        />\n        <Moon\n          size={iconSize}\n          className={cn(\n            'transition-all duration-300',\n            theme === 'dark'\n              ? 'rotate-0 scale-100 opacity-100'\n              : '-rotate-90 scale-0 opacity-0'\n          )}\n        />\n      </div>\n    </button>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AAYO,SAAS,YAAY,KAA4C;QAA5C,EAAE,SAAS,EAAE,OAAO,IAAI,EAAoB,GAA5C;;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,2CACA;gBACE,WAAW,SAAS;gBACpB,aAAa,SAAS;gBACtB,aAAa,SAAS;YACxB,GACA;;;;;;IAIR;IAEA,MAAM,cAAc;QAClB,SAAS,UAAU,UAAU,SAAS;IACxC;IAEA,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN,CAAC,CAAC,KAAK;IAEP,qBACE,6LAAC;QACC,SAAS;QACT,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,8PACA;YACE,WAAW,SAAS;YACpB,aAAa,SAAS;YACtB,aAAa,SAAS;QACxB,GACA;QAEF,cAAY,AAAC,aAAiD,OAArC,UAAU,UAAU,SAAS,SAAQ;kBAE9D,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mMAAA,CAAA,MAAG;oBACF,MAAM;oBACN,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wCACA,UAAU,SACN,gCACA;;;;;;8BAGR,6LAAC,qMAAA,CAAA,OAAI;oBACH,MAAM;oBACN,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+BACA,UAAU,SACN,mCACA;;;;;;;;;;;;;;;;;AAMhB;GAtEgB;;QAEc,mJAAA,CAAA,WAAQ;;;KAFtB", "debugId": null}}, {"offset": {"line": 1578, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/i18n/config.ts"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport { getRequestConfig } from 'next-intl/server'\n\n// Can be imported from a shared config\nexport const locales = ['bn', 'en', 'ar'] as const\nexport const defaultLocale = 'bn' as const\n\nexport type Locale = typeof locales[number]\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as any)) notFound()\n\n  return {\n    messages: (await import(`../../../messages/${locale}.json`)).default\n  }\n})\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,MAAM,UAAU;IAAC;IAAM;IAAM;CAAK;AAClC,MAAM,gBAAgB;6CAId,CAAA,GAAA,mMAAA,CAAA,mBAAgB,AAAD,OAAE;QAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAgB,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IAE7C,OAAO;QACL,UAAU,CAAC;;;;;;;;;;;;;kBAAa,AAAC,qBAA2B,OAAP,QAAO,SAAO,EAAE,OAAO;IACtE;AACF", "debugId": null}}, {"offset": {"line": 1626, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/layout/language-selector.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRouter, usePathname } from 'next/navigation'\nimport { useLocale } from 'next-intl'\nimport { ChevronDown, Languages } from 'lucide-react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { cn } from '@/lib/utils'\nimport { locales } from '@/lib/i18n/config'\n\nconst LANGUAGE_NAMES = {\n  bn: 'বাংলা',\n  en: 'English',\n  ar: 'العربية',\n} as const\n\ninterface LanguageSelectorProps {\n  className?: string\n}\n\nexport function LanguageSelector({ className }: LanguageSelectorProps) {\n  const [isOpen, setIsOpen] = useState(false)\n  const router = useRouter()\n  const pathname = usePathname()\n  const locale = useLocale()\n\n  const handleLanguageChange = (newLocale: string) => {\n    // Remove current locale from pathname and add new locale\n    const pathWithoutLocale = pathname.replace(/^\\/[a-z]{2}/, '')\n    const newPath = `/${newLocale}${pathWithoutLocale}`\n    \n    router.push(newPath)\n    setIsOpen(false)\n  }\n\n  return (\n    <div className={cn('relative', className)}>\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className={cn(\n          'flex items-center gap-2 rounded-lg border border-border bg-card px-3 py-2 text-sm font-medium text-card-foreground transition-all duration-200',\n          'hover:bg-accent hover:text-accent-foreground',\n          'focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n          isOpen && 'bg-accent text-accent-foreground'\n        )}\n        aria-label=\"Select language\"\n        aria-expanded={isOpen}\n      >\n        <Languages className=\"h-4 w-4\" />\n        <span className=\"hidden sm:inline\">\n          {LANGUAGE_NAMES[locale as keyof typeof LANGUAGE_NAMES]}\n        </span>\n        <ChevronDown\n          className={cn(\n            'h-4 w-4 transition-transform duration-200',\n            isOpen && 'rotate-180'\n          )}\n        />\n      </button>\n\n      <AnimatePresence>\n        {isOpen && (\n          <>\n            {/* Backdrop */}\n            <div\n              className=\"fixed inset-0 z-40\"\n              onClick={() => setIsOpen(false)}\n            />\n            \n            {/* Dropdown */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.95, y: -10 }}\n              animate={{ opacity: 1, scale: 1, y: 0 }}\n              exit={{ opacity: 0, scale: 0.95, y: -10 }}\n              transition={{ duration: 0.15 }}\n              className=\"absolute right-0 top-full z-50 mt-2 min-w-[150px] rounded-lg border border-border bg-card p-1 shadow-lg\"\n            >\n              {locales.map((lang) => (\n                <button\n                  key={lang}\n                  onClick={() => handleLanguageChange(lang)}\n                  className={cn(\n                    'flex w-full items-center gap-3 rounded-md px-3 py-2 text-sm transition-colors',\n                    'hover:bg-accent hover:text-accent-foreground',\n                    locale === lang && 'bg-primary text-primary-foreground'\n                  )}\n                >\n                  <span className=\"text-lg\">\n                    {lang === 'bn' && '🇧🇩'}\n                    {lang === 'en' && '🇺🇸'}\n                    {lang === 'ar' && '🇸🇦'}\n                  </span>\n                  <span className=\"font-medium\">\n                    {LANGUAGE_NAMES[lang]}\n                  </span>\n                  {locale === lang && (\n                    <span className=\"ml-auto text-xs\">✓</span>\n                  )}\n                </button>\n              ))}\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;;;AARA;;;;;;;;AAUA,MAAM,iBAAiB;IACrB,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AAMO,SAAS,iBAAiB,KAAoC;QAApC,EAAE,SAAS,EAAyB,GAApC;;IAC/B,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,uBAAuB,CAAC;QAC5B,yDAAyD;QACzD,MAAM,oBAAoB,SAAS,OAAO,CAAC,eAAe;QAC1D,MAAM,UAAU,AAAC,IAAe,OAAZ,WAA8B,OAAlB;QAEhC,OAAO,IAAI,CAAC;QACZ,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAC7B,6LAAC;gBACC,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,kJACA,gDACA,uEACA,UAAU;gBAEZ,cAAW;gBACX,iBAAe;;kCAEf,6LAAC,+MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,6LAAC;wBAAK,WAAU;kCACb,cAAc,CAAC,OAAsC;;;;;;kCAExD,6LAAC,uNAAA,CAAA,cAAW;wBACV,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,6CACA,UAAU;;;;;;;;;;;;0BAKhB,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC;;sCAEE,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,UAAU;;;;;;sCAI3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAM,GAAG,CAAC;4BAAG;4BAC3C,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,GAAG;4BAAE;4BACtC,MAAM;gCAAE,SAAS;gCAAG,OAAO;gCAAM,GAAG,CAAC;4BAAG;4BACxC,YAAY;gCAAE,UAAU;4BAAK;4BAC7B,WAAU;sCAET,+HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,qBACZ,6LAAC;oCAEC,SAAS,IAAM,qBAAqB;oCACpC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,iFACA,gDACA,WAAW,QAAQ;;sDAGrB,6LAAC;4CAAK,WAAU;;gDACb,SAAS,QAAQ;gDACjB,SAAS,QAAQ;gDACjB,SAAS,QAAQ;;;;;;;sDAEpB,6LAAC;4CAAK,WAAU;sDACb,cAAc,CAAC,KAAK;;;;;;wCAEtB,WAAW,sBACV,6LAAC;4CAAK,WAAU;sDAAkB;;;;;;;mCAjB/B;;;;;;;;;;;;;;;;;;;;;;;AA2BvB;GAtFgB;;QAEC,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACb,qKAAA,CAAA,YAAS;;;KAJV", "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { useTranslations } from 'next-intl'\nimport { Clock, MapPin } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { LAYOUT_CONFIG } from '@/lib/constants'\nimport { HamburgerMenu } from '@/components/navigation/hamburger-menu'\nimport { ThemeToggle } from '@/components/theme/theme-toggle'\nimport { LanguageSelector } from './language-selector'\n\ninterface HeaderProps {\n  className?: string\n}\n\nexport function Header({ className }: HeaderProps) {\n  const t = useTranslations('common')\n\n  return (\n    <header\n      className={cn(\n        'sticky top-0 z-40 border-b border-border bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60',\n        'transition-theme',\n        className\n      )}\n      style={{ height: LAYOUT_CONFIG.header.height }}\n    >\n      <div className=\"container mx-auto flex h-full items-center justify-between px-4 lg:px-6\">\n        {/* Left section */}\n        <div className=\"flex items-center gap-4\">\n          {/* Hamburger menu */}\n          <HamburgerMenu className=\"lg:hidden\" />\n          \n          {/* Logo and title - hidden on mobile when sidebar is present */}\n          <div className=\"hidden items-center gap-3 sm:flex lg:flex\">\n            <div className=\"h-8 w-8 rounded-lg bg-primary flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold text-sm\">SG</span>\n            </div>\n            <div>\n              <h1 className=\"font-bold text-lg text-foreground\">Salat Guide</h1>\n              <p className=\"text-xs text-muted-foreground\">ইসলামিক নামাজের গাইড</p>\n            </div>\n          </div>\n        </div>\n\n        {/* Center section - Prayer time info (hidden on mobile) */}\n        <div className=\"hidden items-center gap-4 md:flex\">\n          <div className=\"flex items-center gap-2 rounded-lg bg-muted px-3 py-2\">\n            <Clock className=\"h-4 w-4 text-primary\" />\n            <div className=\"text-sm\">\n              <span className=\"font-medium text-foreground\">পরবর্তী: যুহর</span>\n              <span className=\"ml-2 text-muted-foreground\">২ ঘন্টা ৩০ মিনিট</span>\n            </div>\n          </div>\n          \n          <div className=\"flex items-center gap-2 rounded-lg bg-muted px-3 py-2\">\n            <MapPin className=\"h-4 w-4 text-primary\" />\n            <span className=\"text-sm text-foreground\">ঢাকা, বাংলাদেশ</span>\n          </div>\n        </div>\n\n        {/* Right section */}\n        <div className=\"flex items-center gap-2\">\n          {/* Language selector */}\n          <LanguageSelector />\n          \n          {/* Theme toggle */}\n          <ThemeToggle />\n          \n          {/* Mobile hamburger menu (shown when sidebar is collapsed) */}\n          <HamburgerMenu className=\"hidden lg:block\" size=\"sm\" />\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;AARA;;;;;;;;AAcO,SAAS,OAAO,KAA0B;QAA1B,EAAE,SAAS,EAAe,GAA1B;;IACrB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,2GACA,oBACA;QAEF,OAAO;YAAE,QAAQ,oIAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,MAAM;QAAC;kBAE7C,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,wJAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCAGzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;8CAE9D,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAMnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;sDAC9C,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;sCAIjD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCAAK,WAAU;8CAA0B;;;;;;;;;;;;;;;;;;8BAK9C,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,uJAAA,CAAA,mBAAgB;;;;;sCAGjB,6LAAC,iJAAA,CAAA,cAAW;;;;;sCAGZ,6LAAC,wJAAA,CAAA,gBAAa;4BAAC,WAAU;4BAAkB,MAAK;;;;;;;;;;;;;;;;;;;;;;;AAK1D;GA5DgB;;QACJ,yMAAA,CAAA,kBAAe;;;KADX", "debugId": null}}, {"offset": {"line": 2043, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/layout/main-layout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode } from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\nimport { useSidebar, useSwipe } from '@/lib/hooks'\nimport { LAYOUT_CONFIG } from '@/lib/constants'\nimport { Sidebar } from '@/components/navigation/sidebar'\nimport { Header } from './header'\nimport { GeometricGridPattern } from '@/components/theme/geometric-pattern'\n\ninterface MainLayoutProps {\n  children: ReactNode\n  className?: string\n}\n\nexport function MainLayout({ children, className }: MainLayoutProps) {\n  const { isOpen, isMobile, openSidebar } = useSidebar()\n\n  // Swipe gesture to open sidebar on mobile\n  const swipeRef = useSwipe({\n    onSwipeRight: () => {\n      if (isMobile && !isOpen) {\n        openSidebar()\n      }\n    },\n  })\n\n  const sidebarWidth = isOpen \n    ? LAYOUT_CONFIG.sidebar.width \n    : LAYOUT_CONFIG.sidebar.collapsedWidth\n\n  return (\n    <div \n      ref={swipeRef}\n      className=\"min-h-screen bg-background transition-theme\"\n    >\n      {/* Background pattern */}\n      <GeometricGridPattern \n        opacity={0.02} \n        className=\"fixed inset-0 z-0\" \n      />\n      \n      <div className=\"relative z-10 flex min-h-screen\">\n        {/* Sidebar */}\n        <Sidebar />\n\n        {/* Main content area */}\n        <div className=\"flex flex-1 flex-col\">\n          {/* Header */}\n          <Header />\n\n          {/* Main content */}\n          <motion.main\n            initial={false}\n            animate={{\n              marginLeft: isMobile ? 0 : (isOpen ? sidebarWidth : LAYOUT_CONFIG.sidebar.collapsedWidth),\n            }}\n            transition={{ duration: 0.3, ease: 'easeInOut' }}\n            className={cn(\n              'flex-1 transition-theme',\n              'lg:ml-0', // Reset margin on large screens since sidebar is relative\n              className\n            )}\n          >\n            {/* Content wrapper */}\n            <div className=\"container mx-auto px-4 py-6 lg:px-6 lg:py-8\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.4, ease: 'easeOut' }}\n                className=\"mx-auto max-w-4xl\"\n              >\n                {children}\n              </motion.div>\n            </div>\n          </motion.main>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;;;AATA;;;;;;;;AAgBO,SAAS,WAAW,KAAwC;QAAxC,EAAE,QAAQ,EAAE,SAAS,EAAmB,GAAxC;;IACzB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,aAAU,AAAD;IAEnD,0CAA0C;IAC1C,MAAM,WAAW,CAAA,GAAA,sIAAA,CAAA,WAAQ,AAAD,EAAE;QACxB,YAAY;6CAAE;gBACZ,IAAI,YAAY,CAAC,QAAQ;oBACvB;gBACF;YACF;;IACF;IAEA,MAAM,eAAe,SACjB,oIAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,KAAK,GAC3B,oIAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,cAAc;IAExC,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC,sJAAA,CAAA,uBAAoB;gBACnB,SAAS;gBACT,WAAU;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,8IAAA,CAAA,UAAO;;;;;kCAGR,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,yIAAA,CAAA,SAAM;;;;;0CAGP,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gCACV,SAAS;gCACT,SAAS;oCACP,YAAY,WAAW,IAAK,SAAS,eAAe,oIAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,cAAc;gCAC1F;gCACA,YAAY;oCAAE,UAAU;oCAAK,MAAM;gCAAY;gCAC/C,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,2BACA,WACA;0CAIF,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAU;wCAC7C,WAAU;kDAET;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB;GAjEgB;;QAC4B,wIAAA,CAAA,aAAU;QAGnC,sIAAA,CAAA,WAAQ;;;KAJX", "debugId": null}}]}