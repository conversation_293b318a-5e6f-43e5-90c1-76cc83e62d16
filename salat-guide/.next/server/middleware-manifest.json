{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_c384be7d._.js", "server/edge/chunks/[root-of-the-server]__d9c35fe1._.js", "server/edge/chunks/edge-wrapper_ec87c98c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(bn|en|ar))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(bn|en|ar)/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "s+V+JrhJyjJD/AE2Wrxm1GkMIJk4NJ5HaLBLltm1feQ=", "__NEXT_PREVIEW_MODE_ID": "7ff3a750f4095f2a1fcfc1951c446a14", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "468c12dcf92b60ab65ce1a2170867db085c436a492532d310792d429212d085a", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ad155b2acfc3adc3e7d26572afb69ebd7d43f9eeac40de108fb6428776b0b9dd"}}}, "sortedMiddleware": ["/"], "functions": {}}