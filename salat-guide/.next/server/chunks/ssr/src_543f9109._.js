module.exports = {

"[project]/src/lib/i18n/config.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "defaultLocale": ()=>defaultLocale,
    "locales": ()=>locales
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getRequestConfig.js [app-rsc] (ecmascript) <export default as getRequestConfig>");
;
;
const locales = [
    'bn',
    'en',
    'ar'
];
const defaultLocale = 'bn';
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getRequestConfig$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getRequestConfig$3e$__["getRequestConfig"])(async ({ locale })=>{
    // Validate that the incoming `locale` parameter is valid
    if (!locales.includes(locale)) (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    return {
        messages: (await __turbopack_context__.f({
            "../../../messages/ar.json": {
                id: ()=>"[project]/messages/ar.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/messages/ar.json (json, async loader)")(__turbopack_context__.i)
            },
            "../../../messages/bn.json": {
                id: ()=>"[project]/messages/bn.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/messages/bn.json (json, async loader)")(__turbopack_context__.i)
            },
            "../../../messages/en.json": {
                id: ()=>"[project]/messages/en.json (json, async loader)",
                module: ()=>__turbopack_context__.r("[project]/messages/en.json (json, async loader)")(__turbopack_context__.i)
            }
        }).import(`../../../messages/${locale}.json`)).default
    };
});
}),
"[project]/src/lib/providers/theme-provider.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ThemeProvider": ()=>ThemeProvider,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ThemeProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/providers/theme-provider.tsx <module evaluation>", "ThemeProvider");
const useTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/providers/theme-provider.tsx <module evaluation>", "useTheme");
}),
"[project]/src/lib/providers/theme-provider.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ThemeProvider": ()=>ThemeProvider,
    "useTheme": ()=>useTheme
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ThemeProvider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/providers/theme-provider.tsx", "ThemeProvider");
const useTheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/lib/providers/theme-provider.tsx", "useTheme");
}),
"[project]/src/lib/providers/theme-provider.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$theme$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/providers/theme-provider.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$theme$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/lib/providers/theme-provider.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$theme$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/components/layout/main-layout.tsx [app-rsc] (client reference proxy) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MainLayout": ()=>MainLayout
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const MainLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/main-layout.tsx <module evaluation>", "MainLayout");
}),
"[project]/src/components/layout/main-layout.tsx [app-rsc] (client reference proxy)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MainLayout": ()=>MainLayout
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const MainLayout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/layout/main-layout.tsx", "MainLayout");
}),
"[project]/src/components/layout/main-layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$main$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/layout/main-layout.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$main$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/layout/main-layout.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$main$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/lib/constants/design.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ANIMATIONS": ()=>ANIMATIONS,
    "BREAKPOINTS": ()=>BREAKPOINTS,
    "GEOMETRIC_PATTERNS": ()=>GEOMETRIC_PATTERNS,
    "ISLAMIC_COLORS": ()=>ISLAMIC_COLORS,
    "LAYOUT_CONFIG": ()=>LAYOUT_CONFIG
});
const ISLAMIC_COLORS = {
    emerald: {
        name: 'Emerald',
        light: '#10b981',
        dark: '#34d399',
        description: 'Traditional Islamic green representing paradise and nature'
    },
    gold: {
        name: 'Gold',
        light: '#f59e0b',
        dark: '#fbbf24',
        description: 'Sacred gold representing divine light and wisdom'
    },
    sapphire: {
        name: 'Sapphire',
        light: '#3b82f6',
        dark: '#60a5fa',
        description: 'Deep blue representing the heavens and spirituality'
    },
    pearl: {
        name: 'Pearl',
        light: '#f8fafc',
        dark: '#1e293b',
        description: 'Pure white/dark representing purity and clarity'
    },
    ruby: {
        name: 'Ruby',
        light: '#dc2626',
        dark: '#f87171',
        description: 'Rich red for accents and important elements'
    },
    onyx: {
        name: 'Onyx',
        light: '#374151',
        dark: '#9ca3af',
        description: 'Neutral dark/light for text and secondary elements'
    }
};
const GEOMETRIC_PATTERNS = [
    {
        id: 'octagon-star',
        name: 'Octagon Star',
        viewBox: '0 0 100 100',
        svg: `<path d="M50 10 L70 30 L90 30 L90 50 L70 70 L50 90 L30 70 L10 50 L10 30 L30 30 Z" fill="none" stroke="currentColor" stroke-width="1"/>
          <path d="M50 25 L65 40 L65 60 L50 75 L35 60 L35 40 Z" fill="none" stroke="currentColor" stroke-width="1"/>`
    },
    {
        id: 'arabesque',
        name: 'Arabesque',
        viewBox: '0 0 100 100',
        svg: `<path d="M20 50 Q30 30, 50 50 Q70 70, 80 50 Q70 30, 50 50 Q30 70, 20 50" fill="none" stroke="currentColor" stroke-width="1"/>
          <path d="M50 20 Q70 30, 50 50 Q30 70, 50 80 Q70 70, 50 50 Q30 30, 50 20" fill="none" stroke="currentColor" stroke-width="1"/>`
    },
    {
        id: 'geometric-grid',
        name: 'Geometric Grid',
        viewBox: '0 0 100 100',
        svg: `<defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke="currentColor" stroke-width="0.5"/>
            </pattern>
          </defs>
          <rect width="100" height="100" fill="url(#grid)"/>`
    }
];
const LAYOUT_CONFIG = {
    sidebar: {
        width: 280,
        collapsedWidth: 64,
        breakpoint: 768
    },
    header: {
        height: 64
    },
    content: {
        maxWidth: 1200,
        padding: 24
    }
};
const ANIMATIONS = {
    durations: {
        fast: 150,
        normal: 300,
        slow: 500
    },
    easings: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)'
    }
};
const BREAKPOINTS = {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536
};
}),
"[project]/src/lib/constants/navigation.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "FOOTER_LINKS": ()=>FOOTER_LINKS,
    "NAVIGATION_ITEMS": ()=>NAVIGATION_ITEMS,
    "SOCIAL_LINKS": ()=>SOCIAL_LINKS
});
const NAVIGATION_ITEMS = [
    {
        id: 'home',
        title: 'হোম',
        href: '/',
        icon: 'Home'
    },
    {
        id: 'prayer-times',
        title: 'নামাজের সময়',
        href: '/prayer-times',
        icon: 'Clock'
    },
    {
        id: 'prayer-guide',
        title: 'নামাজের নিয়ম',
        icon: 'Book',
        children: [
            {
                id: 'wudu',
                title: 'অজু',
                href: '/prayer-guide/wudu',
                icon: 'Droplets'
            },
            {
                id: 'fajr',
                title: 'ফজর',
                href: '/prayer-guide/fajr',
                icon: 'Sunrise'
            },
            {
                id: 'dhuhr',
                title: 'যুহর',
                href: '/prayer-guide/dhuhr',
                icon: 'Sun'
            },
            {
                id: 'asr',
                title: 'আসর',
                href: '/prayer-guide/asr',
                icon: 'CloudSun'
            },
            {
                id: 'maghrib',
                title: 'মাগরিব',
                href: '/prayer-guide/maghrib',
                icon: 'Sunset'
            },
            {
                id: 'isha',
                title: 'ইশা',
                href: '/prayer-guide/isha',
                icon: 'Moon'
            }
        ]
    },
    {
        id: 'duas',
        title: 'দোয়া সমূহ',
        icon: 'Heart',
        children: [
            {
                id: 'daily-duas',
                title: 'দৈনন্দিন দোয়া',
                href: '/duas/daily',
                icon: 'Calendar'
            },
            {
                id: 'prayer-duas',
                title: 'নামাজের দোয়া',
                href: '/duas/prayer',
                icon: 'Hands'
            },
            {
                id: 'special-duas',
                title: 'বিশেষ দোয়া',
                href: '/duas/special',
                icon: 'Star'
            }
        ]
    },
    {
        id: 'qibla',
        title: 'কিবলার দিক',
        href: '/qibla',
        icon: 'Compass'
    },
    {
        id: 'islamic-calendar',
        title: 'ইসলামিক ক্যালেন্ডার',
        href: '/calendar',
        icon: 'CalendarDays'
    },
    {
        id: 'learning',
        title: 'শিক্ষা',
        icon: 'GraduationCap',
        children: [
            {
                id: 'basics',
                title: 'মৌলিক বিষয়',
                href: '/learning/basics',
                icon: 'BookOpen'
            },
            {
                id: 'arabic',
                title: 'আরবি শেখা',
                href: '/learning/arabic',
                icon: 'Languages'
            },
            {
                id: 'quran',
                title: 'কুরআন তিলাওয়াত',
                href: '/learning/quran',
                icon: 'BookMarked'
            }
        ]
    },
    {
        id: 'settings',
        title: 'সেটিংস',
        href: '/settings',
        icon: 'Settings'
    }
];
const FOOTER_LINKS = [
    {
        title: 'সম্পর্কে',
        href: '/about'
    },
    {
        title: 'যোগাযোগ',
        href: '/contact'
    },
    {
        title: 'গোপনীয়তা নীতি',
        href: '/privacy'
    },
    {
        title: 'ব্যবহারের শর্তাবলী',
        href: '/terms'
    }
];
const SOCIAL_LINKS = [
    {
        name: 'Facebook',
        href: '#',
        icon: 'Facebook'
    },
    {
        name: 'Twitter',
        href: '#',
        icon: 'Twitter'
    },
    {
        name: 'Instagram',
        href: '#',
        icon: 'Instagram'
    },
    {
        name: 'YouTube',
        href: '#',
        icon: 'Youtube'
    }
];
}),
"[project]/src/lib/constants/index.ts [app-rsc] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "API_CONFIG": ()=>API_CONFIG,
    "APP_CONFIG": ()=>APP_CONFIG,
    "DEFAULT_PRAYER_TIMES": ()=>DEFAULT_PRAYER_TIMES,
    "STORAGE_KEYS": ()=>STORAGE_KEYS
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$design$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/design.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$navigation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/navigation.ts [app-rsc] (ecmascript)");
;
;
const APP_CONFIG = {
    name: 'Salat Guide',
    description: 'Complete Islamic prayer guide in Bengali',
    version: '1.0.0',
    author: 'Salat Guide Team',
    keywords: [
        'islam',
        'prayer',
        'salat',
        'bengali',
        'muslim',
        'guide'
    ],
    url: 'https://salat-guide.com',
    locale: {
        default: 'bn',
        supported: [
            'bn',
            'en',
            'ar'
        ]
    }
};
const API_CONFIG = {
    prayerTimes: 'https://api.aladhan.com/v1',
    qibla: 'https://api.aladhan.com/v1/qibla',
    islamicCalendar: 'https://api.aladhan.com/v1/gToH'
};
const STORAGE_KEYS = {
    theme: 'salat-guide-theme',
    locale: 'salat-guide-locale',
    location: 'salat-guide-location',
    sidebarState: 'salat-guide-sidebar',
    userPreferences: 'salat-guide-preferences'
};
const DEFAULT_PRAYER_TIMES = {
    fajr: '05:30',
    dhuhr: '12:15',
    asr: '15:45',
    maghrib: '18:30',
    isha: '20:00'
};
}),
"[project]/src/lib/constants/index.ts [app-rsc] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$design$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/design.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$navigation$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants/navigation.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/constants/index.ts [app-rsc] (ecmascript) <locals>");
}),
"[project]/src/lib/utils/metadata.ts [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "generateIslamicStructuredData": ()=>generateIslamicStructuredData,
    "generateMetadata": ()=>generateMetadata
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/lib/constants/index.ts [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/lib/constants/index.ts [app-rsc] (ecmascript) <locals>");
;
function generateMetadata({ title, description = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].description, keywords = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].keywords, locale = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].locale.default, path = '', image, type = 'website', publishedTime, modifiedTime, author = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].author } = {}) {
    const fullTitle = title ? `${title} | ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].name}` : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].name;
    const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url}${path}`;
    const defaultImage = `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url}/og-image.jpg`;
    return {
        title: fullTitle,
        description,
        keywords: keywords.join(', '),
        authors: [
            {
                name: author
            }
        ],
        creator: author,
        publisher: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].name,
        // Open Graph
        openGraph: {
            type,
            locale,
            url,
            title: fullTitle,
            description,
            siteName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].name,
            images: [
                {
                    url: image || defaultImage,
                    width: 1200,
                    height: 630,
                    alt: fullTitle
                }
            ],
            ...type === 'article' && {
                publishedTime,
                modifiedTime,
                authors: [
                    author
                ]
            }
        },
        // Twitter
        twitter: {
            card: 'summary_large_image',
            title: fullTitle,
            description,
            images: [
                image || defaultImage
            ],
            creator: '@salatguide'
        },
        // Additional metadata
        robots: {
            index: true,
            follow: true,
            googleBot: {
                index: true,
                follow: true,
                'max-video-preview': -1,
                'max-image-preview': 'large',
                'max-snippet': -1
            }
        },
        // Verification
        verification: {
            google: 'your-google-verification-code',
            yandex: 'your-yandex-verification-code',
            yahoo: 'your-yahoo-verification-code'
        },
        // Alternate languages
        alternates: {
            canonical: url,
            languages: {
                'bn': `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url}/bn${path}`,
                'en': `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url}/en${path}`,
                'ar': `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url}/ar${path}`
            }
        },
        // App-specific
        applicationName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].name,
        category: 'Religion & Spirituality',
        classification: 'Islamic Prayer Guide',
        // Manifest
        manifest: '/manifest.json',
        // Icons
        icons: {
            icon: [
                {
                    url: '/favicon-16x16.png',
                    sizes: '16x16',
                    type: 'image/png'
                },
                {
                    url: '/favicon-32x32.png',
                    sizes: '32x32',
                    type: 'image/png'
                }
            ],
            apple: [
                {
                    url: '/apple-touch-icon.png',
                    sizes: '180x180',
                    type: 'image/png'
                }
            ],
            other: [
                {
                    rel: 'mask-icon',
                    url: '/safari-pinned-tab.svg',
                    color: '#10b981'
                }
            ]
        }
    };
}
function generateIslamicStructuredData({ title, description, url, datePublished, dateModified, author = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].name, image }) {
    return {
        '@context': 'https://schema.org',
        '@type': 'Article',
        headline: title,
        description,
        url,
        datePublished,
        dateModified,
        author: {
            '@type': 'Organization',
            name: author,
            url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url
        },
        publisher: {
            '@type': 'Organization',
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].name,
            url: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url,
            logo: {
                '@type': 'ImageObject',
                url: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].url}/logo.png`
            }
        },
        mainEntityOfPage: {
            '@type': 'WebPage',
            '@id': url
        },
        ...image && {
            image: {
                '@type': 'ImageObject',
                url: image,
                width: 1200,
                height: 630
            }
        },
        about: {
            '@type': 'Thing',
            name: 'Islamic Prayer',
            description: 'Islamic prayer guidance and religious practices'
        },
        keywords: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2f$index$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["APP_CONFIG"].keywords.join(', '),
        inLanguage: 'bn'
    };
}
}),
"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>LocaleLayout,
    "generateMetadata": ()=>generateMetadata,
    "generateStaticParams": ()=>generateStaticParams
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$NextIntlClientProviderServer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__NextIntlClientProvider$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-server/NextIntlClientProviderServer.js [app-rsc] (ecmascript) <export default as NextIntlClientProvider>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getMessages$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getMessages$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getMessages.js [app-rsc] (ecmascript) <export default as getMessages>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-rsc] (ecmascript) <export default as getTranslations>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$api$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/next/dist/api/navigation.react-server.js [app-rsc] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/components/navigation.react-server.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$theme$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/providers/theme-provider.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$main$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/layout/main-layout.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n/config.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils/metadata.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
function generateStaticParams() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["locales"].map((locale)=>({
            locale
        }));
}
async function generateMetadata({ params }) {
    const { locale } = await params;
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'meta'
    });
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2f$metadata$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["generateMetadata"])({
        title: t('title'),
        description: t('description'),
        keywords: t('keywords').split(', '),
        locale
    });
}
async function LocaleLayout({ children, params }) {
    const { locale } = await params;
    // Validate that the incoming `locale` parameter is valid
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2f$config$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["locales"].includes(locale)) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$components$2f$navigation$2e$react$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["notFound"])();
    }
    // Providing all messages to the client
    // side is the easiest way to get started
    const messages = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getMessages$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getMessages$3e$__["getMessages"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$server$2f$NextIntlClientProviderServer$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__NextIntlClientProvider$3e$__["NextIntlClientProvider"], {
        messages: messages,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$providers$2f$theme$2d$provider$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ThemeProvider"], {
            attribute: "data-theme",
            defaultTheme: "light",
            enableSystem: true,
            disableTransitionOnChange: false,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$layout$2f$main$2d$layout$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MainLayout"], {
                children: children
            }, void 0, false, {
                fileName: "[project]/src/app/[locale]/layout.tsx",
                lineNumber: 53,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/[locale]/layout.tsx",
            lineNumber: 47,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/[locale]/layout.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=src_543f9109._.js.map