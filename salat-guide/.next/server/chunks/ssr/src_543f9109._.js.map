{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/i18n/config.ts"], "sourcesContent": ["import { notFound } from 'next/navigation'\nimport { getRequestConfig } from 'next-intl/server'\n\n// Can be imported from a shared config\nexport const locales = ['bn', 'en', 'ar'] as const\nexport const defaultLocale = 'bn' as const\n\nexport type Locale = typeof locales[number]\n\nexport default getRequestConfig(async ({ locale }) => {\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as any)) notFound()\n\n  return {\n    messages: (await import(`../../../messages/${locale}.json`)).default\n  }\n})\n"], "names": [], "mappings": ";;;;;AAAA;AAAA;AACA;;;AAGO,MAAM,UAAU;IAAC;IAAM;IAAM;CAAK;AAClC,MAAM,gBAAgB;uCAId,CAAA,GAAA,0PAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,EAAE,MAAM,EAAE;IAC/C,yDAAyD;IACzD,IAAI,CAAC,QAAQ,QAAQ,CAAC,SAAgB,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IAE7C,OAAO;QACL,UAAU,CAAC;;;;;;;;;;;;;kBAAa,CAAC,kBAAkB,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACtE;AACF", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/providers/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/providers/theme-provider.tsx <module evaluation>\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,sEACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,sEACA", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/providers/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\nexport const useTheme = registerClientReference(\n    function() { throw new Error(\"Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/lib/providers/theme-provider.tsx\",\n    \"useTheme\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kDACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kDACA", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx <module evaluation>\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,uEACA", "debugId": null}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/components/layout/main-layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const MainLayout = registerClientReference(\n    function() { throw new Error(\"Attempted to call MainLayout() from the server but MainLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/main-layout.tsx\",\n    \"MainLayout\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,mDACA", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/constants/design.ts"], "sourcesContent": ["import type { IslamicColor, GeometricPattern, LayoutConfig } from '@/types/global'\n\n/**\n * Islamic color palette for light and dark themes\n */\nexport const ISLAMIC_COLORS: Record<string, IslamicColor> = {\n  emerald: {\n    name: 'Emerald',\n    light: '#10b981',\n    dark: '#34d399',\n    description: 'Traditional Islamic green representing paradise and nature'\n  },\n  gold: {\n    name: 'Gold',\n    light: '#f59e0b',\n    dark: '#fbbf24',\n    description: 'Sacred gold representing divine light and wisdom'\n  },\n  sapphire: {\n    name: 'Sapphire',\n    light: '#3b82f6',\n    dark: '#60a5fa',\n    description: 'Deep blue representing the heavens and spirituality'\n  },\n  pearl: {\n    name: '<PERSON>',\n    light: '#f8fafc',\n    dark: '#1e293b',\n    description: 'Pure white/dark representing purity and clarity'\n  },\n  ruby: {\n    name: 'Ruby',\n    light: '#dc2626',\n    dark: '#f87171',\n    description: 'Rich red for accents and important elements'\n  },\n  onyx: {\n    name: 'Onyx',\n    light: '#374151',\n    dark: '#9ca3af',\n    description: 'Neutral dark/light for text and secondary elements'\n  }\n}\n\n/**\n * Islamic geometric patterns (SVG paths)\n */\nexport const GEOMETRIC_PATTERNS: GeometricPattern[] = [\n  {\n    id: 'octagon-star',\n    name: 'Octagon Star',\n    viewBox: '0 0 100 100',\n    svg: `<path d=\"M50 10 L70 30 L90 30 L90 50 L70 70 L50 90 L30 70 L10 50 L10 30 L30 30 Z\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>\n          <path d=\"M50 25 L65 40 L65 60 L50 75 L35 60 L35 40 Z\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>`\n  },\n  {\n    id: 'arabesque',\n    name: 'Arabesque',\n    viewBox: '0 0 100 100',\n    svg: `<path d=\"M20 50 Q30 30, 50 50 Q70 70, 80 50 Q70 30, 50 50 Q30 70, 20 50\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>\n          <path d=\"M50 20 Q70 30, 50 50 Q30 70, 50 80 Q70 70, 50 50 Q30 30, 50 20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"1\"/>`\n  },\n  {\n    id: 'geometric-grid',\n    name: 'Geometric Grid',\n    viewBox: '0 0 100 100',\n    svg: `<defs>\n            <pattern id=\"grid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n              <path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"0.5\"/>\n            </pattern>\n          </defs>\n          <rect width=\"100\" height=\"100\" fill=\"url(#grid)\"/>`\n  }\n]\n\n/**\n * Layout configuration\n */\nexport const LAYOUT_CONFIG: LayoutConfig = {\n  sidebar: {\n    width: 280,\n    collapsedWidth: 64,\n    breakpoint: 768, // md breakpoint\n  },\n  header: {\n    height: 64,\n  },\n  content: {\n    maxWidth: 1200,\n    padding: 24,\n  }\n}\n\n/**\n * Animation durations and easings\n */\nexport const ANIMATIONS = {\n  durations: {\n    fast: 150,\n    normal: 300,\n    slow: 500,\n  },\n  easings: {\n    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',\n    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',\n    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',\n  }\n}\n\n/**\n * Breakpoints for responsive design\n */\nexport const BREAKPOINTS = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536,\n} as const\n"], "names": [], "mappings": ";;;;;;;AAKO,MAAM,iBAA+C;IAC1D,SAAS;QACP,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,MAAM;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,UAAU;QACR,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,OAAO;QACL,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,MAAM;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;IACA,MAAM;QACJ,MAAM;QACN,OAAO;QACP,MAAM;QACN,aAAa;IACf;AACF;AAKO,MAAM,qBAAyC;IACpD;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,KAAK,CAAC;oHAC0G,CAAC;IACnH;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,KAAK,CAAC;uIAC6H,CAAC;IACtI;IACA;QACE,IAAI;QACJ,MAAM;QACN,SAAS;QACT,KAAK,CAAC;;;;;4DAKkD,CAAC;IAC3D;CACD;AAKM,MAAM,gBAA8B;IACzC,SAAS;QACP,OAAO;QACP,gBAAgB;QAChB,YAAY;IACd;IACA,QAAQ;QACN,QAAQ;IACV;IACA,SAAS;QACP,UAAU;QACV,SAAS;IACX;AACF;AAKO,MAAM,aAAa;IACxB,WAAW;QACT,MAAM;QACN,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,WAAW;QACX,SAAS;QACT,QAAQ;IACV;AACF;AAKO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/constants/navigation.ts"], "sourcesContent": ["import type { NavigationItem } from '@/types/global'\n\n/**\n * Main navigation structure for the Salat Guide\n */\nexport const NAVIGATION_ITEMS: NavigationItem[] = [\n  {\n    id: 'home',\n    title: 'হোম',\n    href: '/',\n    icon: 'Home',\n  },\n  {\n    id: 'prayer-times',\n    title: 'নামাজের সময়',\n    href: '/prayer-times',\n    icon: 'Clock',\n  },\n  {\n    id: 'prayer-guide',\n    title: 'নামাজের নিয়ম',\n    icon: 'Book',\n    children: [\n      {\n        id: 'wudu',\n        title: 'অজু',\n        href: '/prayer-guide/wudu',\n        icon: 'Droplets',\n      },\n      {\n        id: 'fajr',\n        title: 'ফজর',\n        href: '/prayer-guide/fajr',\n        icon: 'Sunrise',\n      },\n      {\n        id: 'dhuhr',\n        title: 'যুহর',\n        href: '/prayer-guide/dhuhr',\n        icon: 'Sun',\n      },\n      {\n        id: 'asr',\n        title: 'আসর',\n        href: '/prayer-guide/asr',\n        icon: 'CloudSun',\n      },\n      {\n        id: 'maghrib',\n        title: 'মাগরিব',\n        href: '/prayer-guide/maghrib',\n        icon: 'Sunset',\n      },\n      {\n        id: 'isha',\n        title: 'ইশা',\n        href: '/prayer-guide/isha',\n        icon: 'Moon',\n      },\n    ],\n  },\n  {\n    id: 'duas',\n    title: 'দোয়া সমূহ',\n    icon: 'Heart',\n    children: [\n      {\n        id: 'daily-duas',\n        title: 'দৈনন্দিন দোয়া',\n        href: '/duas/daily',\n        icon: 'Calendar',\n      },\n      {\n        id: 'prayer-duas',\n        title: 'নামাজের দোয়া',\n        href: '/duas/prayer',\n        icon: 'Hands',\n      },\n      {\n        id: 'special-duas',\n        title: 'বিশেষ দোয়া',\n        href: '/duas/special',\n        icon: 'Star',\n      },\n    ],\n  },\n  {\n    id: 'qibla',\n    title: 'কিবলার দিক',\n    href: '/qibla',\n    icon: 'Compass',\n  },\n  {\n    id: 'islamic-calendar',\n    title: 'ইসলামিক ক্যালেন্ডার',\n    href: '/calendar',\n    icon: 'CalendarDays',\n  },\n  {\n    id: 'learning',\n    title: 'শিক্ষা',\n    icon: 'GraduationCap',\n    children: [\n      {\n        id: 'basics',\n        title: 'মৌলিক বিষয়',\n        href: '/learning/basics',\n        icon: 'BookOpen',\n      },\n      {\n        id: 'arabic',\n        title: 'আরবি শেখা',\n        href: '/learning/arabic',\n        icon: 'Languages',\n      },\n      {\n        id: 'quran',\n        title: 'কুরআন তিলাওয়াত',\n        href: '/learning/quran',\n        icon: 'BookMarked',\n      },\n    ],\n  },\n  {\n    id: 'settings',\n    title: 'সেটিংস',\n    href: '/settings',\n    icon: 'Settings',\n  },\n]\n\n/**\n * Footer navigation links\n */\nexport const FOOTER_LINKS = [\n  {\n    title: 'সম্পর্কে',\n    href: '/about',\n  },\n  {\n    title: 'যোগাযোগ',\n    href: '/contact',\n  },\n  {\n    title: 'গোপনীয়তা নীতি',\n    href: '/privacy',\n  },\n  {\n    title: 'ব্যবহারের শর্তাবলী',\n    href: '/terms',\n  },\n]\n\n/**\n * Social media links\n */\nexport const SOCIAL_LINKS = [\n  {\n    name: 'Facebook',\n    href: '#',\n    icon: 'Facebook',\n  },\n  {\n    name: 'Twitter',\n    href: '#',\n    icon: 'Twitter',\n  },\n  {\n    name: 'Instagram',\n    href: '#',\n    icon: 'Instagram',\n  },\n  {\n    name: 'YouTube',\n    href: '#',\n    icon: 'Youtube',\n  },\n]\n"], "names": [], "mappings": ";;;;;AAKO,MAAM,mBAAqC;IAChD;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;YACR;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;IACR;CACD;AAKM,MAAM,eAAe;IAC1B;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;IACA;QACE,OAAO;QACP,MAAM;IACR;CACD;AAKM,MAAM,eAAe;IAC1B;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM;IACR;CACD", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/constants/index.ts"], "sourcesContent": ["export * from './design'\nexport * from './navigation'\n\n/**\n * Application metadata\n */\nexport const APP_CONFIG = {\n  name: 'Salat Guide',\n  description: 'Complete Islamic prayer guide in Bengali',\n  version: '1.0.0',\n  author: 'Salat Guide Team',\n  keywords: ['islam', 'prayer', 'salat', 'bengali', 'muslim', 'guide'],\n  url: 'https://salat-guide.com',\n  locale: {\n    default: 'bn',\n    supported: ['bn', 'en', 'ar'],\n  },\n} as const\n\n/**\n * API endpoints and external services\n */\nexport const API_CONFIG = {\n  prayerTimes: 'https://api.aladhan.com/v1',\n  qibla: 'https://api.aladhan.com/v1/qibla',\n  islamicCalendar: 'https://api.aladhan.com/v1/gToH',\n} as const\n\n/**\n * Local storage keys\n */\nexport const STORAGE_KEYS = {\n  theme: 'salat-guide-theme',\n  locale: 'salat-guide-locale',\n  location: 'salat-guide-location',\n  sidebarState: 'salat-guide-sidebar',\n  userPreferences: 'salat-guide-preferences',\n} as const\n\n/**\n * Default prayer times (fallback)\n */\nexport const DEFAULT_PRAYER_TIMES = {\n  fajr: '05:30',\n  dhuhr: '12:15',\n  asr: '15:45',\n  maghrib: '18:30',\n  isha: '20:00',\n} as const\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAKO,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,SAAS;IACT,QAAQ;IACR,UAAU;QAAC;QAAS;QAAU;QAAS;QAAW;QAAU;KAAQ;IACpE,KAAK;IACL,QAAQ;QACN,SAAS;QACT,WAAW;YAAC;YAAM;YAAM;SAAK;IAC/B;AACF;AAKO,MAAM,aAAa;IACxB,aAAa;IACb,OAAO;IACP,iBAAiB;AACnB;AAKO,MAAM,eAAe;IAC1B,OAAO;IACP,QAAQ;IACR,UAAU;IACV,cAAc;IACd,iBAAiB;AACnB;AAKO,MAAM,uBAAuB;IAClC,MAAM;IACN,OAAO;IACP,KAAK;IACL,SAAS;IACT,MAAM;AACR", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/lib/utils/metadata.ts"], "sourcesContent": ["import type { Metadata } from 'next/metadata'\nimport { APP_CONFIG } from '@/lib/constants'\n\ninterface GenerateMetadataProps {\n  title?: string\n  description?: string\n  keywords?: string[]\n  locale?: string\n  path?: string\n  image?: string\n  type?: 'website' | 'article'\n  publishedTime?: string\n  modifiedTime?: string\n  author?: string\n}\n\nexport function generateMetadata({\n  title,\n  description = APP_CONFIG.description,\n  keywords = APP_CONFIG.keywords,\n  locale = APP_CONFIG.locale.default,\n  path = '',\n  image,\n  type = 'website',\n  publishedTime,\n  modifiedTime,\n  author = APP_CONFIG.author,\n}: GenerateMetadataProps = {}): Metadata {\n  const fullTitle = title ? `${title} | ${APP_CONFIG.name}` : APP_CONFIG.name\n  const url = `${APP_CONFIG.url}${path}`\n  const defaultImage = `${APP_CONFIG.url}/og-image.jpg`\n\n  return {\n    title: fullTitle,\n    description,\n    keywords: keywords.join(', '),\n    authors: [{ name: author }],\n    creator: author,\n    publisher: APP_CONFIG.name,\n    \n    // Open Graph\n    openGraph: {\n      type,\n      locale,\n      url,\n      title: fullTitle,\n      description,\n      siteName: APP_CONFIG.name,\n      images: [\n        {\n          url: image || defaultImage,\n          width: 1200,\n          height: 630,\n          alt: fullTitle,\n        },\n      ],\n      ...(type === 'article' && {\n        publishedTime,\n        modifiedTime,\n        authors: [author],\n      }),\n    },\n    \n    // Twitter\n    twitter: {\n      card: 'summary_large_image',\n      title: fullTitle,\n      description,\n      images: [image || defaultImage],\n      creator: '@salatguide',\n    },\n    \n    // Additional metadata\n    robots: {\n      index: true,\n      follow: true,\n      googleBot: {\n        index: true,\n        follow: true,\n        'max-video-preview': -1,\n        'max-image-preview': 'large',\n        'max-snippet': -1,\n      },\n    },\n    \n    // Verification\n    verification: {\n      google: 'your-google-verification-code',\n      yandex: 'your-yandex-verification-code',\n      yahoo: 'your-yahoo-verification-code',\n    },\n    \n    // Alternate languages\n    alternates: {\n      canonical: url,\n      languages: {\n        'bn': `${APP_CONFIG.url}/bn${path}`,\n        'en': `${APP_CONFIG.url}/en${path}`,\n        'ar': `${APP_CONFIG.url}/ar${path}`,\n      },\n    },\n    \n    // App-specific\n    applicationName: APP_CONFIG.name,\n    category: 'Religion & Spirituality',\n    classification: 'Islamic Prayer Guide',\n    \n    // Manifest\n    manifest: '/manifest.json',\n    \n    // Icons\n    icons: {\n      icon: [\n        { url: '/favicon-16x16.png', sizes: '16x16', type: 'image/png' },\n        { url: '/favicon-32x32.png', sizes: '32x32', type: 'image/png' },\n      ],\n      apple: [\n        { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },\n      ],\n      other: [\n        { rel: 'mask-icon', url: '/safari-pinned-tab.svg', color: '#10b981' },\n      ],\n    },\n  }\n}\n\n/**\n * Generate structured data for Islamic content\n */\nexport function generateIslamicStructuredData({\n  title,\n  description,\n  url,\n  datePublished,\n  dateModified,\n  author = APP_CONFIG.name,\n  image,\n}: {\n  title: string\n  description: string\n  url: string\n  datePublished?: string\n  dateModified?: string\n  author?: string\n  image?: string\n}) {\n  return {\n    '@context': 'https://schema.org',\n    '@type': 'Article',\n    headline: title,\n    description,\n    url,\n    datePublished,\n    dateModified,\n    author: {\n      '@type': 'Organization',\n      name: author,\n      url: APP_CONFIG.url,\n    },\n    publisher: {\n      '@type': 'Organization',\n      name: APP_CONFIG.name,\n      url: APP_CONFIG.url,\n      logo: {\n        '@type': 'ImageObject',\n        url: `${APP_CONFIG.url}/logo.png`,\n      },\n    },\n    mainEntityOfPage: {\n      '@type': 'WebPage',\n      '@id': url,\n    },\n    ...(image && {\n      image: {\n        '@type': 'ImageObject',\n        url: image,\n        width: 1200,\n        height: 630,\n      },\n    }),\n    about: {\n      '@type': 'Thing',\n      name: 'Islamic Prayer',\n      description: 'Islamic prayer guidance and religious practices',\n    },\n    keywords: APP_CONFIG.keywords.join(', '),\n    inLanguage: 'bn',\n  }\n}\n"], "names": [], "mappings": ";;;;AACA;AAAA;;AAeO,SAAS,iBAAiB,EAC/B,KAAK,EACL,cAAc,gJAAA,CAAA,aAAU,CAAC,WAAW,EACpC,WAAW,gJAAA,CAAA,aAAU,CAAC,QAAQ,EAC9B,SAAS,gJAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO,EAClC,OAAO,EAAE,EACT,KAAK,EACL,OAAO,SAAS,EAChB,aAAa,EACb,YAAY,EACZ,SAAS,gJAAA,CAAA,aAAU,CAAC,MAAM,EACJ,GAAG,CAAC,CAAC;IAC3B,MAAM,YAAY,QAAQ,GAAG,MAAM,GAAG,EAAE,gJAAA,CAAA,aAAU,CAAC,IAAI,EAAE,GAAG,gJAAA,CAAA,aAAU,CAAC,IAAI;IAC3E,MAAM,MAAM,GAAG,gJAAA,CAAA,aAAU,CAAC,GAAG,GAAG,MAAM;IACtC,MAAM,eAAe,GAAG,gJAAA,CAAA,aAAU,CAAC,GAAG,CAAC,aAAa,CAAC;IAErD,OAAO;QACL,OAAO;QACP;QACA,UAAU,SAAS,IAAI,CAAC;QACxB,SAAS;YAAC;gBAAE,MAAM;YAAO;SAAE;QAC3B,SAAS;QACT,WAAW,gJAAA,CAAA,aAAU,CAAC,IAAI;QAE1B,aAAa;QACb,WAAW;YACT;YACA;YACA;YACA,OAAO;YACP;YACA,UAAU,gJAAA,CAAA,aAAU,CAAC,IAAI;YACzB,QAAQ;gBACN;oBACE,KAAK,SAAS;oBACd,OAAO;oBACP,QAAQ;oBACR,KAAK;gBACP;aACD;YACD,GAAI,SAAS,aAAa;gBACxB;gBACA;gBACA,SAAS;oBAAC;iBAAO;YACnB,CAAC;QACH;QAEA,UAAU;QACV,SAAS;YACP,MAAM;YACN,OAAO;YACP;YACA,QAAQ;gBAAC,SAAS;aAAa;YAC/B,SAAS;QACX;QAEA,sBAAsB;QACtB,QAAQ;YACN,OAAO;YACP,QAAQ;YACR,WAAW;gBACT,OAAO;gBACP,QAAQ;gBACR,qBAAqB,CAAC;gBACtB,qBAAqB;gBACrB,eAAe,CAAC;YAClB;QACF;QAEA,eAAe;QACf,cAAc;YACZ,QAAQ;YACR,QAAQ;YACR,OAAO;QACT;QAEA,sBAAsB;QACtB,YAAY;YACV,WAAW;YACX,WAAW;gBACT,MAAM,GAAG,gJAAA,CAAA,aAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM;gBACnC,MAAM,GAAG,gJAAA,CAAA,aAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM;gBACnC,MAAM,GAAG,gJAAA,CAAA,aAAU,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM;YACrC;QACF;QAEA,eAAe;QACf,iBAAiB,gJAAA,CAAA,aAAU,CAAC,IAAI;QAChC,UAAU;QACV,gBAAgB;QAEhB,WAAW;QACX,UAAU;QAEV,QAAQ;QACR,OAAO;YACL,MAAM;gBACJ;oBAAE,KAAK;oBAAsB,OAAO;oBAAS,MAAM;gBAAY;gBAC/D;oBAAE,KAAK;oBAAsB,OAAO;oBAAS,MAAM;gBAAY;aAChE;YACD,OAAO;gBACL;oBAAE,KAAK;oBAAyB,OAAO;oBAAW,MAAM;gBAAY;aACrE;YACD,OAAO;gBACL;oBAAE,KAAK;oBAAa,KAAK;oBAA0B,OAAO;gBAAU;aACrE;QACH;IACF;AACF;AAKO,SAAS,8BAA8B,EAC5C,KAAK,EACL,WAAW,EACX,GAAG,EACH,aAAa,EACb,YAAY,EACZ,SAAS,gJAAA,CAAA,aAAU,CAAC,IAAI,EACxB,KAAK,EASN;IACC,OAAO;QACL,YAAY;QACZ,SAAS;QACT,UAAU;QACV;QACA;QACA;QACA;QACA,QAAQ;YACN,SAAS;YACT,MAAM;YACN,KAAK,gJAAA,CAAA,aAAU,CAAC,GAAG;QACrB;QACA,WAAW;YACT,SAAS;YACT,MAAM,gJAAA,CAAA,aAAU,CAAC,IAAI;YACrB,KAAK,gJAAA,CAAA,aAAU,CAAC,GAAG;YACnB,MAAM;gBACJ,SAAS;gBACT,KAAK,GAAG,gJAAA,CAAA,aAAU,CAAC,GAAG,CAAC,SAAS,CAAC;YACnC;QACF;QACA,kBAAkB;YAChB,SAAS;YACT,OAAO;QACT;QACA,GAAI,SAAS;YACX,OAAO;gBACL,SAAS;gBACT,KAAK;gBACL,OAAO;gBACP,QAAQ;YACV;QACF,CAAC;QACD,OAAO;YACL,SAAS;YACT,MAAM;YACN,aAAa;QACf;QACA,UAAU,gJAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,IAAI,CAAC;QACnC,YAAY;IACd;AACF", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///Volumes/Others/projects/salat-guide-augment/salat-guide/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import { NextIntlClientProvider } from 'next-intl'\nimport { getMessages, getTranslations } from 'next-intl/server'\nimport { notFound } from 'next/navigation'\nimport { ThemeProvider } from '@/lib/providers/theme-provider'\nimport { MainLayout } from '@/components/layout/main-layout'\nimport { locales } from '@/lib/i18n/config'\nimport { generateMetadata as generateMetadataUtil } from '@/lib/utils/metadata'\n\ninterface LocaleLayoutProps {\n  children: React.ReactNode\n  params: Promise<{ locale: string }>\n}\n\nexport function generateStaticParams() {\n  return locales.map((locale) => ({ locale }))\n}\n\nexport async function generateMetadata({ params }: LocaleLayoutProps) {\n  const { locale } = await params\n  const t = await getTranslations({ locale, namespace: 'meta' })\n\n  return generateMetadataUtil({\n    title: t('title'),\n    description: t('description'),\n    keywords: t('keywords').split(', '),\n    locale,\n  })\n}\n\nexport default async function LocaleLayout({\n  children,\n  params\n}: LocaleLayoutProps) {\n  const { locale } = await params\n\n  // Validate that the incoming `locale` parameter is valid\n  if (!locales.includes(locale as any)) {\n    notFound()\n  }\n\n  // Providing all messages to the client\n  // side is the easiest way to get started\n  const messages = await getMessages()\n\n  return (\n    <NextIntlClientProvider messages={messages}>\n      <ThemeProvider\n        attribute=\"data-theme\"\n        defaultTheme=\"light\"\n        enableSystem\n        disableTransitionOnChange={false}\n      >\n        <MainLayout>\n          {children}\n        </MainLayout>\n      </ThemeProvider>\n    </NextIntlClientProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;AAOO,SAAS;IACd,OAAO,4HAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,SAAW,CAAC;YAAE;QAAO,CAAC;AAC5C;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAqB;IAClE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,CAAA,GAAA,wPAAA,CAAA,kBAAe,AAAD,EAAE;QAAE;QAAQ,WAAW;IAAO;IAE5D,OAAO,CAAA,GAAA,+HAAA,CAAA,mBAAoB,AAAD,EAAE;QAC1B,OAAO,EAAE;QACT,aAAa,EAAE;QACf,UAAU,EAAE,YAAY,KAAK,CAAC;QAC9B;IACF;AACF;AAEe,eAAe,aAAa,EACzC,QAAQ,EACR,MAAM,EACY;IAClB,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,yDAAyD;IACzD,IAAI,CAAC,4HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,SAAgB;QACpC,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD;IACT;IAEA,uCAAuC;IACvC,yCAAyC;IACzC,MAAM,WAAW,MAAM,CAAA,GAAA,gPAAA,CAAA,cAAW,AAAD;IAEjC,qBACE,8OAAC,kQAAA,CAAA,yBAAsB;QAAC,UAAU;kBAChC,cAAA,8OAAC,6IAAA,CAAA,gBAAa;YACZ,WAAU;YACV,cAAa;YACb,YAAY;YACZ,2BAA2B;sBAE3B,cAAA,8OAAC,8IAAA,CAAA,aAAU;0BACR;;;;;;;;;;;;;;;;AAKX", "debugId": null}}]}